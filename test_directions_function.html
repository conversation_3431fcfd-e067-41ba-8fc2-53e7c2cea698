<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Directions Function</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>Test Calendar Map Directions Function</h2>
        <p>This page tests the getDirectionsToEvent function that was added to the calendar map.</p>
        
        <div class="card">
            <div class="card-body">
                <h5>Test Event Popup</h5>
                <div class="info-window">
                    <h6>Test Car Show Event</h6>
                    <p class="mb-1"><small>December 20, 2024 10:00 AM - 4:00 PM</small></p>
                    <p class="mb-1"><small>123 Main Street, Anytown, ST 12345</small></p>
                    <div class="mt-2">
                        <button onclick="testGetDirections()" class="btn btn-sm btn-success me-2">
                            <i class="fas fa-route me-1"></i>Get Directions
                        </button>
                        <a href="#" class="btn btn-sm btn-primary">View Details</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <h5>Test Results:</h5>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        // Simulate the global function from the calendar map
        function getDirectionsToEvent(eventLocation, eventTitle) {
            // Simulate user address (in real implementation this comes from PHP)
            const userAddress = {
                address: "456 User Street",
                city: "User City",
                state: "UC",
                zip: "54321"
            };
            
            if (!userAddress) {
                alert('Please add your address to your profile to get directions.');
                return;
            }
            
            const fromAddress = `${userAddress.address}, ${userAddress.city}, ${userAddress.state} ${userAddress.zip}`;
            const toAddress = eventLocation;
            
            // For testing, just log instead of opening new window
            console.log('Opening directions from:', fromAddress, 'to:', toAddress);
            
            const directionsUrl = `https://www.google.com/maps/dir/${encodeURIComponent(fromAddress)}/${encodeURIComponent(toAddress)}`;
            
            document.getElementById('test-results').innerHTML = `
                <div class="alert alert-success">
                    <h6>✅ Function executed successfully!</h6>
                    <p><strong>From:</strong> ${fromAddress}</p>
                    <p><strong>To:</strong> ${toAddress}</p>
                    <p><strong>URL:</strong> <a href="${directionsUrl}" target="_blank">${directionsUrl}</a></p>
                </div>
            `;
        }
        
        function testGetDirections() {
            try {
                getDirectionsToEvent('123 Main Street, Anytown, ST 12345', 'Test Car Show Event');
            } catch (error) {
                document.getElementById('test-results').innerHTML = `
                    <div class="alert alert-danger">
                        <h6>❌ Function failed!</h6>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Test if function is defined
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof getDirectionsToEvent === 'function') {
                document.getElementById('test-results').innerHTML = `
                    <div class="alert alert-info">
                        <p>✅ getDirectionsToEvent function is defined and ready to test.</p>
                        <p>Click the "Get Directions" button above to test it.</p>
                    </div>
                `;
            } else {
                document.getElementById('test-results').innerHTML = `
                    <div class="alert alert-danger">
                        <p>❌ getDirectionsToEvent function is not defined!</p>
                    </div>
                `;
            }
        });
    </script>
    
    <style>
        .info-window {
            min-width: 250px;
            max-width: 300px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .info-window h6 {
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }
        
        .info-window .btn {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: 0.25rem;
            text-decoration: none;
            display: inline-block;
            line-height: 1.2;
            box-shadow: none;
        }
        
        .info-window .btn:hover,
        .info-window .btn:focus {
            box-shadow: none;
        }
    </style>
</body>
</html>