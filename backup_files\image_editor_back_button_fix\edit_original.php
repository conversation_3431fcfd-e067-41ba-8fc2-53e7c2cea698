<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Image Editor</h1>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($_SESSION['image_editor_referer'])): ?>
                <a href="<?php echo $_SESSION['image_editor_referer']; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back
                </a>
            <?php elseif ($image->entity_type == 'vehicle'): ?>
                <a href="<?php echo BASE_URL; ?>/user/vehicle_images/<?php echo $image->entity_id; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Vehicle Images
                </a>
            <?php elseif ($image->entity_type == 'show'): ?>
                <a href="<?php echo BASE_URL; ?>/show/images/<?php echo $image->entity_id; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Show Images
                </a>
            <?php else: ?>
                <a href="<?php echo BASE_URL; ?>/image_editor/browse" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Image Browser
                </a>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Image Preview</h5>
                </div>
                <div class="card-body text-center">
                    <img src="<?php echo $image_url; ?>?v=<?php echo isset($cache_buster) ? $cache_buster : time(); ?>" alt="Image" class="img-fluid" id="previewImage">
                    <div class="mt-3">
                        <p>
                            <strong>Dimensions:</strong> <?php echo $width; ?> x <?php echo $height; ?> pixels<br>
                            <strong>File Size:</strong> <?php echo number_format(filesize($image->file_path) / 1024, 2); ?> KB<br>
                            <strong>File Type:</strong> <?php echo $image->file_type; ?>
                        </p>
                        <button type="button" class="btn btn-sm btn-secondary" id="reloadImageBtn">
                            <i class="fas fa-sync-alt me-2"></i> Reload Image
                        </button>
                    </div>
                    
                    <script>
                        document.getElementById('reloadImageBtn').addEventListener('click', function() {
                            const img = document.getElementById('previewImage');
                            const currentSrc = img.src.split('?')[0];
                            img.src = currentSrc + '?v=' + new Date().getTime();
                        });
                    </script>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Editing Tools</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="<?php echo BASE_URL; ?>/image_editor/crop/<?php echo $image->id; ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-crop-alt me-2"></i> Crop Image
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/resize/<?php echo $image->id; ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-expand-arrows-alt me-2"></i> Resize Image
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/rotate/<?php echo $image->id; ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-redo me-2"></i> Rotate Image
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/filter/<?php echo $image->id; ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-filter me-2"></i> Apply Filter
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/text/<?php echo $image->id; ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-font me-2"></i> Add Text
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/draw/<?php echo $image->id; ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-paint-brush me-2"></i> Draw on Image
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/optimize/<?php echo $image->id; ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-compress-arrows-alt me-2"></i> Optimize Image
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Image Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($image->is_primary != 1): ?>
                            <a href="<?php echo BASE_URL; ?>/image_editor/setPrimary/<?php echo $image->id; ?>" class="btn btn-success">
                                <i class="fas fa-star me-2"></i> Set as Primary Image
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo BASE_URL; ?>/image_editor/delete/<?php echo $image->id; ?>" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i> Delete Image
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>