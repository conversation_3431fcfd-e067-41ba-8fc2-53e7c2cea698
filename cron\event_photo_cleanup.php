<?php
/**
 * Event Photo Cleanup Cron Job
 * 
 * This script automatically cleans up old event photos based on engagement levels
 * and retention policies set in the admin settings.
 * 
 * Usage: php event_photo_cleanup.php [--dry-run] [--verbose]
 * 
 * Schedule: Run daily via cron
 * Example crontab entry: 0 2 * * * /usr/bin/php /path/to/cron/event_photo_cleanup.php
 */

// Prevent direct web access
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from the command line.');
}

// Set up paths
define('APPROOT', dirname(__DIR__));
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/libraries/Database.php';

class EventPhotoCleanup {
    private $db;
    private $settings;
    private $dryRun = false;
    private $verbose = false;
    private $stats = [
        'total_photos' => 0,
        'high_engagement' => 0,
        'medium_engagement' => 0,
        'low_engagement' => 0,
        'deleted_photos' => 0,
        'freed_space' => 0,
        'errors' => 0
    ];

    public function __construct() {
        $this->db = new Database();
        $this->loadSettings();
        $this->parseArguments();
    }

    /**
     * Parse command line arguments
     */
    private function parseArguments() {
        global $argv;
        
        if (isset($argv)) {
            foreach ($argv as $arg) {
                if ($arg === '--dry-run') {
                    $this->dryRun = true;
                }
                if ($arg === '--verbose') {
                    $this->verbose = true;
                }
            }
        }
    }

    /**
     * Load cleanup settings from database
     */
    private function loadSettings() {
        try {
            $this->db->query('SELECT setting_name, setting_value FROM event_photo_settings');
            $results = $this->db->resultSet();
            
            $this->settings = [];
            foreach ($results as $setting) {
                $this->settings[$setting->setting_name] = $setting->setting_value;
            }
            
            // Set defaults if settings don't exist
            $defaults = [
                'auto_cleanup_enabled' => '0',
                'high_engagement_threshold' => '10',
                'medium_engagement_threshold' => '3',
                'high_engagement_retention_months' => '24',
                'medium_engagement_retention_months' => '12',
                'low_engagement_retention_months' => '6',
                'cleanup_frequency_days' => '30',
                'max_storage_gb' => '50'
            ];
            
            foreach ($defaults as $key => $value) {
                if (!isset($this->settings[$key])) {
                    $this->settings[$key] = $value;
                }
            }
            
        } catch (Exception $e) {
            $this->log("Error loading settings: " . $e->getMessage(), true);
            exit(1);
        }
    }

    /**
     * Main cleanup execution
     */
    public function run() {
        $this->log("=== Event Photo Cleanup Started ===");
        $this->log("Mode: " . ($this->dryRun ? "DRY RUN" : "LIVE"));
        
        // Check if cleanup is enabled
        if ($this->settings['auto_cleanup_enabled'] !== '1') {
            $this->log("Auto cleanup is disabled. Exiting.");
            return;
        }
        
        // Check if it's time to run cleanup
        if (!$this->shouldRunCleanup()) {
            $this->log("Cleanup not due yet. Exiting.");
            return;
        }
        
        try {
            // Get all event photos
            $this->getEventPhotos();
            
            // Categorize by engagement
            $this->categorizeByEngagement();
            
            // Clean up expired photos
            $this->cleanupExpiredPhotos();
            
            // Update last cleanup time
            if (!$this->dryRun) {
                $this->updateLastCleanupTime();
            }
            
            // Generate report
            $this->generateReport();
            
        } catch (Exception $e) {
            $this->log("Error during cleanup: " . $e->getMessage(), true);
            $this->stats['errors']++;
        }
        
        $this->log("=== Event Photo Cleanup Completed ===");
    }

    /**
     * Check if cleanup should run based on frequency settings
     */
    private function shouldRunCleanup() {
        try {
            $this->db->query('SELECT setting_value FROM event_photo_settings WHERE setting_name = "last_cleanup_run"');
            $lastRun = $this->db->single();
            
            if (!$lastRun) {
                return true; // Never run before
            }
            
            $lastRunTime = strtotime($lastRun->setting_value);
            $frequencyDays = intval($this->settings['cleanup_frequency_days']);
            $nextRunTime = $lastRunTime + ($frequencyDays * 24 * 60 * 60);
            
            return time() >= $nextRunTime;
            
        } catch (Exception $e) {
            $this->log("Error checking cleanup schedule: " . $e->getMessage(), true);
            return false;
        }
    }

    /**
     * Get all event photos from database
     */
    private function getEventPhotos() {
        try {
            $this->db->query('
                SELECT i.*, epm.category, epm.caption, epm.privacy_level, epm.event_type, epm.event_id,
                       COUNT(DISTINCT il.id) as likes_count,
                       COUNT(DISTINCT ic.id) as comments_count,
                       COUNT(DISTINCT iv.id) as views_count
                FROM images i
                LEFT JOIN event_photo_metadata epm ON i.id = epm.image_id
                LEFT JOIN image_likes il ON i.id = il.image_id
                LEFT JOIN image_comments ic ON i.id = ic.image_id  
                LEFT JOIN image_views iv ON i.id = iv.image_id
                WHERE i.entity_type = "event_photo"
                GROUP BY i.id
                ORDER BY i.created_at DESC
            ');
            
            $this->photos = $this->db->resultSet();
            $this->stats['total_photos'] = count($this->photos);
            
            $this->log("Found {$this->stats['total_photos']} event photos to analyze");
            
        } catch (Exception $e) {
            $this->log("Error fetching event photos: " . $e->getMessage(), true);
            throw $e;
        }
    }

    /**
     * Categorize photos by engagement level
     */
    private function categorizeByEngagement() {
        $highThreshold = intval($this->settings['high_engagement_threshold']);
        $mediumThreshold = intval($this->settings['medium_engagement_threshold']);
        
        foreach ($this->photos as $photo) {
            $engagement = ($photo->likes_count ?? 0) + ($photo->comments_count ?? 0) + 
                         floor(($photo->views_count ?? 0) / 10); // Views count less
            
            if ($engagement >= $highThreshold) {
                $photo->engagement_level = 'high';
                $this->stats['high_engagement']++;
            } elseif ($engagement >= $mediumThreshold) {
                $photo->engagement_level = 'medium';
                $this->stats['medium_engagement']++;
            } else {
                $photo->engagement_level = 'low';
                $this->stats['low_engagement']++;
            }
            
            $photo->engagement_score = $engagement;
        }
        
        $this->log("Engagement categorization: High={$this->stats['high_engagement']}, Medium={$this->stats['medium_engagement']}, Low={$this->stats['low_engagement']}");
    }

    /**
     * Clean up photos that have exceeded their retention period
     */
    private function cleanupExpiredPhotos() {
        $now = time();
        $deletedCount = 0;
        $freedSpace = 0;
        
        foreach ($this->photos as $photo) {
            $photoAge = $now - strtotime($photo->created_at);
            $retentionMonths = $this->getRetentionMonths($photo->engagement_level);
            $retentionSeconds = $retentionMonths * 30 * 24 * 60 * 60; // Approximate
            
            if ($photoAge > $retentionSeconds) {
                $this->log("Deleting expired photo: ID={$photo->id}, Age=" . round($photoAge / (24*60*60)) . " days, Engagement={$photo->engagement_level}");
                
                if (!$this->dryRun) {
                    if ($this->deletePhoto($photo)) {
                        $deletedCount++;
                        $freedSpace += $photo->file_size ?? 0;
                    }
                } else {
                    $deletedCount++;
                    $freedSpace += $photo->file_size ?? 0;
                }
            }
        }
        
        $this->stats['deleted_photos'] = $deletedCount;
        $this->stats['freed_space'] = $freedSpace;
        
        $this->log("Cleanup completed: {$deletedCount} photos deleted, " . $this->formatBytes($freedSpace) . " freed");
    }

    /**
     * Get retention period in months for engagement level
     */
    private function getRetentionMonths($engagementLevel) {
        switch ($engagementLevel) {
            case 'high':
                return intval($this->settings['high_engagement_retention_months']);
            case 'medium':
                return intval($this->settings['medium_engagement_retention_months']);
            case 'low':
            default:
                return intval($this->settings['low_engagement_retention_months']);
        }
    }

    /**
     * Delete a photo and its associated files
     */
    private function deletePhoto($photo) {
        try {
            // Delete physical files
            if (!empty($photo->file_path) && file_exists(APPROOT . '/' . $photo->file_path)) {
                unlink(APPROOT . '/' . $photo->file_path);
            }
            
            if (!empty($photo->thumbnail_path) && file_exists(APPROOT . '/' . $photo->thumbnail_path)) {
                unlink(APPROOT . '/' . $photo->thumbnail_path);
            }
            
            // Delete from event_photo_metadata
            $this->db->query('DELETE FROM event_photo_metadata WHERE image_id = :image_id');
            $this->db->bind(':image_id', $photo->id);
            $this->db->execute();
            
            // Delete from images table
            $this->db->query('DELETE FROM images WHERE id = :id');
            $this->db->bind(':id', $photo->id);
            $this->db->execute();
            
            return true;
            
        } catch (Exception $e) {
            $this->log("Error deleting photo ID {$photo->id}: " . $e->getMessage(), true);
            $this->stats['errors']++;
            return false;
        }
    }

    /**
     * Update last cleanup run time
     */
    private function updateLastCleanupTime() {
        try {
            $this->db->query('
                INSERT INTO event_photo_settings (setting_name, setting_value) 
                VALUES ("last_cleanup_run", :timestamp) 
                ON DUPLICATE KEY UPDATE setting_value = :timestamp
            ');
            $this->db->bind(':timestamp', date('Y-m-d H:i:s'));
            $this->db->execute();
            
        } catch (Exception $e) {
            $this->log("Error updating last cleanup time: " . $e->getMessage(), true);
        }
    }

    /**
     * Generate cleanup report
     */
    private function generateReport() {
        $this->log("\n=== CLEANUP REPORT ===");
        $this->log("Total Photos Analyzed: {$this->stats['total_photos']}");
        $this->log("High Engagement: {$this->stats['high_engagement']}");
        $this->log("Medium Engagement: {$this->stats['medium_engagement']}");
        $this->log("Low Engagement: {$this->stats['low_engagement']}");
        $this->log("Photos Deleted: {$this->stats['deleted_photos']}");
        $this->log("Space Freed: " . $this->formatBytes($this->stats['freed_space']));
        $this->log("Errors: {$this->stats['errors']}");
        $this->log("======================\n");
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Log message with timestamp
     */
    private function log($message, $isError = false) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}";
        
        if ($this->verbose || $isError) {
            echo $logMessage . "\n";
        }
        
        // Also log to file
        $logFile = APPROOT . '/logs/event_photo_cleanup.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logMessage . "\n", FILE_APPEND | LOCK_EX);
    }
}

// Run the cleanup
try {
    $cleanup = new EventPhotoCleanup();
    $cleanup->run();
} catch (Exception $e) {
    echo "Fatal error: " . $e->getMessage() . "\n";
    exit(1);
}
