<?php
/**
 * Dashboard Quick Actions Component
 * Role-specific quick action cards for dashboards
 */

// Helper function to get action icon colors
function getActionIconColor($colorName) {
    $colors = [
        'primary' => 'linear-gradient(135deg, #007bff, #0056b3)',
        'success' => 'linear-gradient(135deg, #28a745, #1e7e34)',
        'info' => 'linear-gradient(135deg, #17a2b8, #117a8b)',
        'warning' => 'linear-gradient(135deg, #ffc107, #e0a800)',
        'danger' => 'linear-gradient(135deg, #dc3545, #c82333)'
    ];
    return $colors[$colorName] ?? $colors['primary'];
}

// Get current user role
$userRole = $_SESSION['user_role'] ?? 'user';
$userId = $_SESSION['user_id'] ?? null;

if (!$userId) {
    return;
}

// Define quick actions for each role
$quickActions = [
    'user' => [
        [
            'icon' => 'fa-car',
            'title' => 'Add Vehicle',
            'description' => 'Register a new vehicle',
            'url' => '/user/addVehicle',
            'color' => 'primary',
            'badge' => null
        ],
        [
            'icon' => 'fa-search',
            'title' => 'Find Shows',
            'description' => 'Browse upcoming car shows',
            'url' => '/show',
            'color' => 'info',
            'badge' => null
        ],
        [
            'icon' => 'fa-plus-circle',
            'title' => 'Create Show',
            'description' => 'Host your own car show',
            'url' => '/user/createShow',
            'color' => 'success',
            'badge' => 'Become Coordinator'
        ],
        [
            'icon' => 'fa-calendar',
            'title' => 'Event Calendar',
            'description' => 'View upcoming events',
            'url' => '/calendar',
            'color' => 'warning',
            'badge' => null
        ]
    ],
    'staff' => [
        [
            'icon' => 'fa-list',
            'title' => 'Manage Registrations',
            'description' => 'Handle show registrations',
            'url' => '/staff/registrations',
            'color' => 'primary',
            'badge' => null
        ],
        [
            'icon' => 'fa-check-circle',
            'title' => 'Vehicle Check-in',
            'description' => 'Check in vehicles',
            'url' => '/staff/checkin',
            'color' => 'success',
            'badge' => null
        ],
        [
            'icon' => 'fa-user-plus',
            'title' => 'Create Registration',
            'description' => 'Register vehicle for user',
            'url' => '/staff/createRegistration',
            'color' => 'info',
            'badge' => null
        ],
        [
            'icon' => 'fa-chart-bar',
            'title' => 'Staff Reports',
            'description' => 'View staff reports',
            'url' => '/staff/reports',
            'color' => 'warning',
            'badge' => null
        ]
    ],
    'judge' => [
        [
            'icon' => 'fa-tasks',
            'title' => 'View Assignments',
            'description' => 'See your judging assignments',
            'url' => '/judge/assignments',
            'color' => 'primary',
            'badge' => null
        ],
        [
            'icon' => 'fa-star',
            'title' => 'Score Vehicles',
            'description' => 'Judge assigned categories',
            'url' => '/judge/score',
            'color' => 'warning',
            'badge' => null
        ],
        [
            'icon' => 'fa-trophy',
            'title' => 'View Results',
            'description' => 'See judging results',
            'url' => '/judge/results',
            'color' => 'success',
            'badge' => null
        ],
        [
            'icon' => 'fa-chart-line',
            'title' => 'Judge Reports',
            'description' => 'Access judging reports',
            'url' => '/judge/reports',
            'color' => 'info',
            'badge' => null
        ]
    ],
    'coordinator' => [
        [
            'icon' => 'fa-plus-circle',
            'title' => 'Create Show',
            'description' => 'Host a new car show',
            'url' => '/user/createShow',
            'color' => 'success',
            'badge' => null
        ],
        [
            'icon' => 'fa-edit',
            'title' => 'Manage Shows',
            'description' => 'Edit your shows',
            'url' => '/admin/shows',
            'color' => 'primary',
            'badge' => null
        ],
        [
            'icon' => 'fa-users',
            'title' => 'Assign Staff',
            'description' => 'Manage show staff',
            'url' => '/admin/showRoleManager',
            'color' => 'info',
            'badge' => null
        ],
        [
            'icon' => 'fa-calendar-plus',
            'title' => 'Create Event',
            'description' => 'Add calendar event',
            'url' => '/calendar/createEvent',
            'color' => 'warning',
            'badge' => null
        ]
    ],
    'admin' => [
        [
            'icon' => 'fa-users',
            'title' => 'User Management',
            'description' => 'Manage all users',
            'url' => '/admin/users',
            'color' => 'primary',
            'badge' => null
        ],
        [
            'icon' => 'fa-calendar-check',
            'title' => 'Show Management',
            'description' => 'Manage all shows',
            'url' => '/admin/shows',
            'color' => 'success',
            'badge' => null
        ],
        [
            'icon' => 'fa-cog',
            'title' => 'System Settings',
            'description' => 'Configure system',
            'url' => '/admin/settings',
            'color' => 'warning',
            'badge' => null
        ],
        [
            'icon' => 'fa-chart-bar',
            'title' => 'System Reports',
            'description' => 'View analytics',
            'url' => '/admin/reports',
            'color' => 'info',
            'badge' => null
        ]
    ]
];

// Get actions for current role
$actions = $quickActions[$userRole] ?? $quickActions['user'];

// Get some dynamic data for badges
try {
    if ($userRole === 'user') {
        // Get user's vehicle count
        require_once APPROOT . '/models/VehicleModel.php';
        $vehicleModel = new VehicleModel();
        $userVehicles = $vehicleModel->getUserVehicles($userId);
        $vehicleCount = count($userVehicles);

        // Update badge for Add Vehicle if user has no vehicles
        if ($vehicleCount === 0) {
            $actions[0]['badge'] = 'Start Here!';
            $actions[0]['color'] = 'danger';
        }
    }
} catch (Exception $e) {
    // Silently fail if models aren't available
}
?>

<div class="dashboard-quick-actions" style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); margin-bottom: 30px;">
    <div class="quick-actions-header" style="text-align: center; margin-bottom: 25px;">
        <h5 style="color: #333; margin-bottom: 8px; font-weight: 700; display: flex; align-items: center; justify-content: center; gap: 8px; font-size: 1.25rem;">
            <i class="fas fa-bolt" style="color: #dc3545; font-size: 1.3rem;"></i>
            Quick Actions
        </h5>
        <p style="margin: 0; font-size: 0.9rem; color: #666;">Get things done faster</p>
    </div>
    
    <div class="quick-actions-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px;">
        <?php foreach ($actions as $action): ?>
            <div class="quick-action-card" onclick="window.location.href='<?php echo BASE_URL . $action['url']; ?>'" style="display: flex; align-items: center; gap: 15px; padding: 20px; border: 2px solid #f8f9fa; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; background: #fafbfc; text-decoration: none;">
                <div class="action-icon bg-<?php echo $action['color']; ?>" style="width: 50px; height: 50px; border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.3rem; flex-shrink: 0; background: <?php echo getActionIconColor($action['color']); ?>;">
                    <i class="fas <?php echo $action['icon']; ?>"></i>
                </div>

                <div class="action-content" style="flex: 1;">
                    <h6 class="action-title" style="color: #333; margin-bottom: 5px; font-weight: 600; font-size: 1rem; display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">
                        <?php echo $action['title']; ?>
                        <?php if ($action['badge']): ?>
                            <span class="action-badge badge" style="font-size: 0.7rem; padding: 2px 6px; border-radius: 4px; font-weight: 500; margin-left: 5px; background-color: #dc3545; color: white;"><?php echo $action['badge']; ?></span>
                        <?php endif; ?>
                    </h6>
                    <p class="action-description" style="color: #666; margin: 0; font-size: 0.85rem; line-height: 1.4;"><?php echo $action['description']; ?></p>
                </div>

                <div class="action-arrow" style="color: #ccc; font-size: 1.1rem; transition: all 0.3s ease;">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<!-- Quick Actions Styles -->
<style>
.dashboard-quick-actions {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.quick-actions-header {
    text-align: center;
    margin-bottom: 25px;
}

.quick-actions-header h5 {
    color: #333;
    margin-bottom: 8px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.quick-actions-header p {
    margin: 0;
    font-size: 0.9rem;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
}

.quick-action-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 2px solid #f8f9fa;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafbfc;
}

.quick-action-card:hover {
    border-color: #dc3545;
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.1);
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    flex-shrink: 0;
}

.action-content {
    flex: 1;
}

.action-title {
    color: #333;
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.action-badge {
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}

.action-description {
    color: #666;
    margin: 0;
    font-size: 0.85rem;
    line-height: 1.4;
}

.action-arrow {
    color: #ccc;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.quick-action-card:hover .action-arrow {
    color: #dc3545;
    transform: translateX(3px);
}

/* Color variations */
.bg-primary { background: linear-gradient(135deg, #007bff, #0056b3); }
.bg-success { background: linear-gradient(135deg, #28a745, #1e7e34); }
.bg-info { background: linear-gradient(135deg, #17a2b8, #117a8b); }
.bg-warning { background: linear-gradient(135deg, #ffc107, #e0a800); }
.bg-danger { background: linear-gradient(135deg, #dc3545, #c82333); }

/* Mobile responsive */
@media (max-width: 768px) {
    .dashboard-quick-actions {
        padding: 20px 15px;
        margin-bottom: 20px;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .quick-action-card {
        padding: 15px;
        gap: 12px;
    }
    
    .action-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
    
    .action-title {
        font-size: 0.9rem;
    }
    
    .action-description {
        font-size: 0.8rem;
    }
}

/* Animation for new user guidance */
@keyframes pulseGlow {
    0%, 100% { 
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.1);
    }
    50% { 
        box-shadow: 0 4px 20px rgba(220, 53, 69, 0.3);
    }
}

.quick-action-card:has(.action-badge:contains("Start Here!")) {
    animation: pulseGlow 2s ease-in-out infinite;
    border-color: #dc3545;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .dashboard-quick-actions {
        background: #2d3748;
        color: white;
    }
    
    .quick-action-card {
        background: #4a5568;
        border-color: #4a5568;
    }
    
    .quick-action-card:hover {
        background: #2d3748;
        border-color: #dc3545;
    }
    
    .action-title {
        color: white;
    }
    
    .action-description {
        color: #cbd5e0;
    }
}
</style>

<!-- Quick Actions JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects and click tracking for analytics
    document.querySelectorAll('.quick-action-card').forEach(card => {
        // Hover effects
        card.addEventListener('mouseenter', function() {
            this.style.borderColor = '#dc3545';
            this.style.background = 'white';
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 15px rgba(220, 53, 69, 0.1)';

            const arrow = this.querySelector('.action-arrow');
            if (arrow) {
                arrow.style.color = '#dc3545';
                arrow.style.transform = 'translateX(3px)';
            }
        });

        card.addEventListener('mouseleave', function() {
            this.style.borderColor = '#f8f9fa';
            this.style.background = '#fafbfc';
            this.style.transform = '';
            this.style.boxShadow = '';

            const arrow = this.querySelector('.action-arrow');
            if (arrow) {
                arrow.style.color = '#ccc';
                arrow.style.transform = '';
            }
        });

        // Click tracking and feedback
        card.addEventListener('click', function() {
            const title = this.querySelector('.action-title').textContent.trim();

            // Track quick action usage
            if (typeof gtag !== 'undefined') {
                gtag('event', 'quick_action_click', {
                    'action_name': title,
                    'user_role': '<?php echo $userRole; ?>'
                });
            }

            // Add visual feedback
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // Highlight first-time user actions
    document.querySelectorAll('.action-badge').forEach(badge => {
        if (badge.textContent.includes('Start Here!')) {
            const card = badge.closest('.quick-action-card');
            if (card) {
                card.style.borderColor = '#dc3545';
                card.style.background = 'white';
                // Add pulse animation
                card.style.animation = 'pulseGlow 2s ease-in-out infinite';
            }
        }
    });
});

// Add pulse animation keyframes if not already defined
if (!document.querySelector('#pulseGlowKeyframes')) {
    const style = document.createElement('style');
    style.id = 'pulseGlowKeyframes';
    style.textContent = `
        @keyframes pulseGlow {
            0%, 100% {
                box-shadow: 0 4px 15px rgba(220, 53, 69, 0.1);
            }
            50% {
                box-shadow: 0 4px 20px rgba(220, 53, 69, 0.3);
            }
        }
    `;
    document.head.appendChild(style);
}
</script>
