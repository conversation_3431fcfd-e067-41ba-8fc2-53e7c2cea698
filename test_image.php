<?php
// Test if the image URL from debug is accessible
$imageUrl = 'https://events.rowaneliterides.com/uploads/event_photos/event_photo_5_688a229fc4af7.png';

echo "<h1>Image Accessibility Test</h1>";

echo "<h2>Image URL:</h2>";
echo "<p>" . htmlspecialchars($imageUrl) . "</p>";

echo "<h2>Image Display Test:</h2>";
echo "<img src='" . htmlspecialchars($imageUrl) . "' alt='Test image' style='max-width: 500px; border: 1px solid #ccc;'>";

echo "<h2>File System Check:</h2>";
$localPath = 'uploads/event_photos/event_photo_5_688a229fc4af7.png';
if (file_exists($localPath)) {
    echo "<p>✅ File exists at: " . $localPath . "</p>";
    echo "<p>File size: " . filesize($localPath) . " bytes</p>";
    
    // Check MIME type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $localPath);
    finfo_close($finfo);
    echo "<p>MIME type: " . $mimeType . "</p>";
    
    // Check if it's a valid image
    $imageInfo = getimagesize($localPath);
    if ($imageInfo) {
        echo "<p>✅ Valid image: " . $imageInfo[0] . "x" . $imageInfo[1] . " pixels</p>";
        echo "<p>Image type: " . $imageInfo['mime'] . "</p>";
    } else {
        echo "<p>❌ Not a valid image file</p>";
    }
} else {
    echo "<p>❌ File does not exist at: " . $localPath . "</p>";
    
    // Check if directory exists
    $dir = dirname($localPath);
    if (is_dir($dir)) {
        echo "<p>Directory exists: " . $dir . "</p>";
        echo "<p>Directory contents:</p>";
        $files = scandir($dir);
        echo "<ul>";
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..') {
                echo "<li>" . htmlspecialchars($file) . "</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<p>❌ Directory does not exist: " . $dir . "</p>";
    }
}

echo "<h2>HTTP Headers Test:</h2>";
echo "<p>Testing HTTP headers for the image URL...</p>";

$headers = @get_headers($imageUrl, 1);
if ($headers) {
    echo "<pre>";
    print_r($headers);
    echo "</pre>";
} else {
    echo "<p>❌ Could not get HTTP headers for the image URL</p>";
}

echo "<h2>cURL Test:</h2>";
if (function_exists('curl_init')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $imageUrl);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $headers = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);
    
    echo "<p>HTTP Status Code: " . $httpCode . "</p>";
    echo "<p>Content Type: " . $contentType . "</p>";
    echo "<pre>" . htmlspecialchars($headers) . "</pre>";
} else {
    echo "<p>cURL not available</p>";
}
?>
