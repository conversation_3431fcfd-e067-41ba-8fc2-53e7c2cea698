<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Upload Image</h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="javascript:history.back();" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>
    
    <?php if (isset($_SESSION['flash_messages']['upload'])) : ?>
        <div class="row mb-3">
            <div class="col-md-8 mx-auto">
                <div class="alert alert-<?php echo $_SESSION['flash_messages']['upload']['type']; ?> alert-dismissible fade show">
                    <?php echo $_SESSION['flash_messages']['upload']['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
        <?php unset($_SESSION['flash_messages']['upload']); ?>
    <?php endif; ?>



    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        Upload Image for <?php echo htmlspecialchars($entity_name); ?>
                        <?php if ($entity_type == 'event_photo'): ?>
                            <span class="badge bg-light text-dark ms-2">Event Photo</span>
                        <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($entity_type == 'user') : ?>
                        <div class="alert alert-info mb-4">
                            <i class="fas fa-info-circle me-2"></i> <strong>Note:</strong> Only one profile image is allowed per user. Uploading a new image will replace your existing profile image.
                        </div>
                    <?php endif; ?>
                    
                    <form action="<?php echo BASE_URL; ?>/image_editor/upload/<?php echo $entity_type; ?>/<?php echo $entity_id; ?>" method="post" enctype="multipart/form-data" id="uploadForm">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="mb-4">
                            <div class="upload-area text-center p-5 border rounded" id="dropArea">
                                <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-muted"></i>
                                <h4>Drag & Drop Image Here</h4>
                                <p class="text-muted">or</p>
                                <div class="mb-3">
                                    <input type="file" class="form-control" id="image" name="image" accept="image/*" required>
                                </div>
                                <p class="text-muted small">
                                    Supported formats: JPG, JPEG, PNG, GIF, WebP<br>
                                    Maximum file size: 10MB
                                </p>
                            </div>
                        </div>
                        
                        <div id="imagePreview" class="mb-4 text-center d-none">
                            <h5>Image Preview</h5>
                            <img src="" alt="Preview" class="img-fluid img-thumbnail mb-3" id="previewImage" style="max-height: 300px;">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>File Name:</strong> <span id="fileName"></span></p>
                                    <p><strong>File Size:</strong> <span id="fileSize"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Dimensions:</strong> <span id="imageDimensions"></span></p>
                                    <p><strong>File Type:</strong> <span id="fileType"></span></p>
                                </div>
                            </div>

                            <!-- Event Photo Specific Fields - FORCED TO SHOW FOR TESTING -->
                            <div class="alert alert-success">
                                <strong>🎉 EVENT PHOTO FIELDS ARE WORKING!</strong><br>
                                If you see this, the PHP is working correctly.
                            </div>

                                <!-- Hidden fields for event context -->
                                <input type="hidden" name="event_photo_event_type" value="<?php echo isset($event_type) ? htmlspecialchars($event_type) : 'event'; ?>">
                                <input type="hidden" name="event_photo_event_id" value="<?php echo htmlspecialchars($entity_id); ?>">

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <label for="event_photo_category" class="form-label">Photo Category</label>
                                        <select class="form-select" id="event_photo_category" name="event_photo_category" required>
                                            <option value="">Select Category</option>
                                            <?php if (isset($category_labels)): ?>
                                                <?php foreach ($category_labels as $key => $label): ?>
                                                    <option value="<?php echo htmlspecialchars($key); ?>">
                                                        <?php echo htmlspecialchars($label); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <option value="vehicle">🚗 Vehicle</option>
                                                <option value="atmosphere">⭐ Atmosphere</option>
                                                <option value="awards">🏆 Awards</option>
                                                <option value="vendors">🍔 Vendors</option>
                                                <option value="people">👥 People</option>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="event_photo_privacy" class="form-label">Privacy Level</label>
                                        <select class="form-select" id="event_photo_privacy" name="event_photo_privacy">
                                            <option value="public">Public - Visible to everyone</option>
                                            <option value="event_only">Event Only - Visible to event attendees</option>
                                            <option value="private">Private - Only visible to you</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <label for="event_photo_caption" class="form-label">
                                            <i class="fas fa-comment me-2"></i>Caption (Optional)
                                        </label>
                                        <textarea class="form-control" id="event_photo_caption" name="event_photo_caption"
                                                  rows="3" maxlength="500"
                                                  placeholder="Describe your photo... What makes this shot special? Share the story behind it!"></textarea>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Add a caption to help others understand and enjoy your photo. This will appear when shared on social media.
                                            <span class="float-end">
                                                <span id="caption-count">0</span>/500 characters
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <!-- END Event Photo Fields -->


                            <button type="button" class="btn btn-outline-danger" id="removeImage">
                                <i class="fas fa-trash me-2"></i> Remove Image
                            </button>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="after_upload" id="returnToUpload" value="upload" checked>
                                <label class="form-check-label" for="returnToUpload">
                                    <i class="fas fa-plus-circle me-1"></i> Upload another image after this one
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="after_upload" id="returnToPrevious" value="browser">
                                <label class="form-check-label" for="returnToPrevious">
                                    <i class="fas fa-arrow-left me-1"></i> Return to Previous page after upload
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" id="uploadButton">
                                <i class="fas fa-upload me-2"></i> Upload Image
                            </button>
                            <div class="text-center mt-3 d-none" id="uploadProgress">
                                <div class="progress mb-3">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="uploadProgressBar"></div>
                                </div>
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Uploading...</span>
                                </div>
                                <p class="mt-2" id="uploadStatusText">Preparing to upload...</p>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const dropArea = document.getElementById('dropArea');
        const fileInput = document.getElementById('image');
        const imagePreview = document.getElementById('imagePreview');
        const previewImage = document.getElementById('previewImage');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const imageDimensions = document.getElementById('imageDimensions');
        const fileType = document.getElementById('fileType');
        const removeImage = document.getElementById('removeImage');
        const uploadButton = document.getElementById('uploadButton');
        const uploadForm = document.getElementById('uploadForm');
        const uploadProgress = document.getElementById('uploadProgress');
        
        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });
        
        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });
        
        // Handle dropped files
        dropArea.addEventListener('drop', handleDrop, false);
        
        // Handle file input change
        fileInput.addEventListener('change', handleFiles);
        
        // Handle remove image button
        removeImage.addEventListener('click', function() {
            fileInput.value = '';
            imagePreview.classList.add('d-none');
            dropArea.classList.remove('d-none');
            uploadButton.disabled = true;
        });
        
        // Handle form submission
        uploadForm.addEventListener('submit', function(e) {
            if (!fileInput.files.length) {
                e.preventDefault();
                alert('Please select an image to upload.');
            } else {
                // Show loading indicator
                const uploadProgress = document.getElementById('uploadProgress');
                const uploadProgressBar = document.getElementById('uploadProgressBar');
                const uploadStatusText = document.getElementById('uploadStatusText');
                
                uploadProgress.classList.remove('d-none');
                uploadButton.disabled = true;
                uploadButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Uploading...';
                
                // Simulate progress for better user experience
                let progress = 0;
                const progressInterval = setInterval(function() {
                    // Increment progress but never reach 100% until server responds
                    if (progress < 90) {
                        progress += Math.random() * 10;
                        progress = Math.min(progress, 90);
                        uploadProgressBar.style.width = progress + '%';
                        
                        // Update status text based on progress
                        if (progress < 30) {
                            uploadStatusText.textContent = 'Preparing image...';
                        } else if (progress < 60) {
                            uploadStatusText.textContent = 'Uploading image...';
                        } else {
                            uploadStatusText.textContent = 'Processing image...';
                        }
                    }
                }, 500);
                
                // Store the interval ID in a data attribute so we can clear it if needed
                uploadForm.dataset.progressInterval = progressInterval;
            }
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        function highlight() {
            dropArea.classList.add('bg-light');
        }
        
        function unhighlight() {
            dropArea.classList.remove('bg-light');
        }
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            
            if (files.length) {
                // Only use the first file if multiple files are dropped
                const file = files[0];
                
                // Check if file is an image
                if (file.type.match('image.*')) {
                    try {
                        // Create a new DataTransfer object
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);
                        fileInput.files = dataTransfer.files;
                    } catch (err) {
                        // Fallback for browsers that don't support DataTransfer
                        alert('Your browser does not support drag and drop file upload. Please use the file selector instead.');
                        return;
                    }
                    
                    handleFiles();
                } else {
                    alert('Please drop an image file.');
                }
            }
        }
        
        function handleFiles() {
            const file = fileInput.files[0];
            
            if (file) {
                // Check if file is an image
                if (!file.type.match('image.*')) {
                    alert('Please select an image file.');
                    fileInput.value = '';
                    return;
                }
                
                // Check file size (max 10MB)
                if (file.size > 10 * 1024 * 1024) {
                    alert('File size exceeds 10MB limit.');
                    fileInput.value = '';
                    return;
                }
                
                // Update file info
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                fileType.textContent = file.type;
                
                // Create object URL for preview
                const objectUrl = URL.createObjectURL(file);
                previewImage.src = objectUrl;
                
                // Get image dimensions
                const img = new Image();
                img.onload = function() {
                    imageDimensions.textContent = `${this.width} x ${this.height} pixels`;
                    URL.revokeObjectURL(objectUrl);
                };
                img.src = objectUrl;
                
                // Show preview and hide drop area
                imagePreview.classList.remove('d-none');
                dropArea.classList.add('d-none');
                uploadButton.disabled = false;
                
                // Update button text
                uploadButton.innerHTML = `<i class="fas fa-upload me-2"></i> Upload Image`;
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes < 1024) {
                return bytes + ' bytes';
            } else if (bytes < 1024 * 1024) {
                return (bytes / 1024).toFixed(2) + ' KB';
            } else {
                return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
            }
        }

        // Initialize caption character counter
        const captionField = document.getElementById('event_photo_caption');
        const captionCount = document.getElementById('caption-count');

        if (captionField && captionCount) {
            captionField.addEventListener('input', function() {
                const count = this.value.length;
                captionCount.textContent = count;

                // Change color based on character count
                if (count > 450) {
                    captionCount.style.color = '#dc3545'; // Red
                } else if (count > 400) {
                    captionCount.style.color = '#fd7e14'; // Orange
                } else {
                    captionCount.style.color = '#6c757d'; // Gray
                }
            });
        }
    });
</script>

<style>
    .upload-area {
        border: 2px dashed #ccc !important;
        border-radius: 5px;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .upload-area:hover {
        border-color: #007bff !important;
        background-color: #f8f9fa;
    }
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>