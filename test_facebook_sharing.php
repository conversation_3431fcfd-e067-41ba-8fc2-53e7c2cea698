<?php
/**
 * Facebook Sharing Test Page
 * 
 * This page helps test and debug Facebook sharing issues.
 * Access it at: https://events.rowaneliterides.com/test_facebook_sharing.php
 */

// Start session
session_start();

// Define the application root directory
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Load helpers
require_once APPROOT . '/helpers/facebook_crawler_helper.php';

// Check if this is a Facebook crawler
$isFacebookBot = isFacebookCrawler();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Sharing Test - <?php echo APP_NAME; ?></title>
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="<?php echo APP_NAME; ?>">
    <meta property="og:title" content="Facebook Sharing Test - <?php echo APP_NAME; ?>">
    <meta property="og:description" content="This is a test page to verify Facebook sharing is working correctly for Rowan Elite Rides events and shows.">
    <meta property="og:url" content="<?php echo BASE_URL; ?>/test_facebook_sharing.php">
    <meta property="og:type" content="website">
    <meta property="og:image" content="<?php echo BASE_URL; ?>/public/images/logo.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 15px; border-radius: 5px; margin: 20px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; }
        .test-links { margin: 20px 0; }
        .test-links a { display: inline-block; margin: 5px 10px 5px 0; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-links a:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Facebook Sharing Test</h1>
        
        <?php if ($isFacebookBot): ?>
            <div class="status success">
                <strong>✅ Facebook Bot Detected!</strong><br>
                This request is coming from Facebook's crawler. The page should display properly for sharing.
            </div>
        <?php else: ?>
            <div class="status info">
                <strong>ℹ️ Regular User Detected</strong><br>
                This request is from a regular user, not Facebook's crawler.
            </div>
        <?php endif; ?>
        
        <h2>📊 Current Status</h2>
        <div class="status info">
            <strong>User Agent:</strong><br>
            <div class="code"><?php echo htmlspecialchars($_SERVER['HTTP_USER_AGENT'] ?? 'Not available'); ?></div>
        </div>
        
        <div class="status info">
            <strong>Request URL:</strong><br>
            <div class="code"><?php echo htmlspecialchars($_SERVER['REQUEST_URI'] ?? 'Not available'); ?></div>
        </div>
        
        <div class="status info">
            <strong>Base URL:</strong><br>
            <div class="code"><?php echo BASE_URL; ?></div>
        </div>
        
        <h2>🧪 Test Facebook Sharing</h2>
        <p>Use Facebook's Sharing Debugger to test this page:</p>
        
        <div class="test-links">
            <a href="https://developers.facebook.com/tools/debug/?q=<?php echo urlencode(BASE_URL . '/test_facebook_sharing.php'); ?>" target="_blank">
                🔍 Test This Page
            </a>
            <a href="https://developers.facebook.com/tools/debug/?q=<?php echo urlencode(BASE_URL); ?>" target="_blank">
                🏠 Test Home Page
            </a>
        </div>
        
        <h2>📋 How to Fix Facebook Sharing Issues</h2>
        <div class="status warning">
            <strong>If you're still seeing "Access Denied" errors:</strong>
            <ol>
                <li><strong>Clear Facebook's cache:</strong> Use the Facebook Sharing Debugger above</li>
                <li><strong>Check authentication:</strong> Make sure public pages don't require login</li>
                <li><strong>Verify Open Graph tags:</strong> Ensure meta tags are properly set</li>
                <li><strong>Test with different URLs:</strong> Try sharing different pages</li>
            </ol>
        </div>
        
        <h2>🔧 Technical Details</h2>
        <div class="status info">
            <strong>Facebook Crawler Detection:</strong><br>
            The system now detects Facebook's crawler and provides appropriate content without authentication requirements.
            
            <br><br><strong>Open Graph Tags:</strong><br>
            All pages now include proper Open Graph meta tags for better Facebook sharing.
            
            <br><br><strong>Crawler User Agents Detected:</strong><br>
            <div class="code">
                • facebookexternalhit<br>
                • Facebot<br>
                • FacebookBot<br>
                • facebook<br>
                • WhatsApp
            </div>
        </div>
        
        <h2>🎯 Next Steps</h2>
        <div class="status success">
            <ol>
                <li>Share this test page on Facebook to verify it works</li>
                <li>Use Facebook's Sharing Debugger to test your main pages</li>
                <li>If issues persist, check server logs for authentication errors</li>
                <li>Contact support if you need further assistance</li>
            </ol>
        </div>
        
        <p><a href="<?php echo BASE_URL; ?>">← Back to Main Site</a></p>
    </div>
</body>
</html>
