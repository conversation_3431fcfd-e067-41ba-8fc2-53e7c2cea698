Debug Info:
Entity Type: event_photo
Entity ID: 5
Entity Name: Name of show
Event Type: show
All Variables: Array ( [view] => image_editor/upload [data] => Array ( [title] => Upload Image [entity_type] => event_photo [entity_id] => 5 [entity_name] => Name of show [csrf_token] => 388b445cc25f513f4cafe1ecd0025d08d35eb989b32c32ae8f214f62c8813bc5 [settings] => Array ( [image_quality] => 80 [thumbnail_size] => 200 [max_upload_size] => 10 [allowed_extensions] => jpg,jpeg,png,gif [optimize_images] => 1 ) [category_labels] => Array ( [vehicle] => 🚗 Vehicle [atmosphere] => ⭐ Atmosphere [awards] => 🏆 Awards [vendors] => 🍔 Vendors [people] => 👥 People ) [event_type] => show [current_user] => stdClass Object ( [id] => 3 [name] => <PERSON> [email] => <EMAIL> [role] => admin [profile_image] => [phone] => 7042023114 [address] => 475 Lemly Ln [city] => Salisbury [state] => NC [zip] => 28146 [timezone] => America/New_York [created_at] => 2025-05-18 18:32:01 [last_login] => 2025-07-30 15:14:37 [status] => active [facebook_id] => 2949931925176821 ) ) [userModel] => UserModel Object ( [db:UserModel:private] => Database Object ( [host:Database:private] => ny-mysql01.offloadsql.com [user:Database:private] => sql24006_forward-limpet [pass:Database:private] => 80kO3&L%HedJv6eb^ [dbname:Database:private] => sql24006_events [dbh:Database:private] => PDO Object ( ) [stmt:Database:private] => PDOStatement Object ( [queryString] => SELECT id, name, email, role, profile_image, phone, address, city, state, zip, timezone, created_at, last_login, status, facebook_id FROM users WHERE id = :id ) [error:Database:private] => ) ) [appRoot] => /home/<USER>/events.rowaneliterides.com [viewPaths] => Array ( [0] => /home/<USER>/events.rowaneliterides.com/views/image_editor/upload.php [1] => /home/<USER>/events.rowaneliterides.com/views/image_editor/upload.php [2] => /home/<USER>/events.rowaneliterides.com/../views/image_editor/upload.php [3] => /home/<USER>/events.rowaneliterides.com/views/image_editor/upload.php ) [viewPath] => /home/<USER>/events.rowaneliterides.com/views/image_editor/upload.php [path] => /home/<USER>/events.rowaneliterides.com/views/image_editor/upload.php [title] => Upload Image [entity_type] => event_photo [entity_id] => 5 [entity_name] => Name of show [csrf_token] => 388b445cc25f513f4cafe1ecd0025d08d35eb989b32c32ae8f214f62c8813bc5 [settings] => Array ( [image_quality] => 80 [thumbnail_size] => 200 [max_upload_size] => 10 [allowed_extensions] => jpg,jpeg,png,gif [optimize_images] => 1 ) [category_labels] => Array ( [vehicle] => 🚗 Vehicle [atmosphere] => ⭐ Atmosphere [awards] => 🏆 Awards [vendors] => 🍔 Vendors [people] => 👥 People ) [event_type] => show [current_user] => stdClass Object ( [id] => 3 [name] => Brian Correll [email] => <EMAIL> [role] => admin [profile_image] => [phone] => 7042023114 [address] => 475 Lemly Ln [city] => Salisbury [state] => NC [zip] => 28146 [timezone] => America/New_York [created_at] => 2025-05-18 18:32:01 [last_login] => 2025-07-30 15:14:37 [status] => active [facebook_id] => 2949931925176821 ) [currentUrl] => /image_editor/upload/event_photo/5 [current_page] => /image_editor/upload/event_photo/5 [is_home_page] => [settingsModel] => SettingsModel Object ( [db:SettingsModel:private] => Database Object ( [host:Database:private] => ny-mysql01.offloadsql.com [user:Database:private] => sql24006_forward-limpet [pass:Database:private] => 80kO3&L%HedJv6eb^ [dbname:Database:private] => sql24006_events [dbh:Database:private] => PDO Object ( ) [stmt:Database:private] => PDOStatement Object ( [queryString] => SELECT setting_value FROM system_settings WHERE setting_key = :key ) [error:Database:private] => ) [groupColumn:SettingsModel:private] => category ) [global_custom_css] => [primary_color] => #4353cb [secondary_color] => #6c757d [accent_color] => #058a07 [background_color] => #f8f9fa [text_color] => #212529 [header_bg_image] => https://events.rowaneliterides.com/public/images/cf.jpg [header_bg_size] => 100% auto [header_bg_position] => center [header_carbon_opacity] => 88 [header_bg_brightness] => 23 [logo_size_desktop] => 62 [logo_size_mobile] => 52 [logo_size_tablet] => 63 [logo_size_apple] => 52 [carbon_overlay_opacity] => 0.704 [site_favicon] => [site_logo] => /uploads/branding/logo_1751468505_rides_logo.png [unread_count] => 0 [unifiedMessageModel] => UnifiedMessageModel Object ( [db:UnifiedMessageModel:private] => Database Object ( [host:Database:private] => ny-mysql01.offloadsql.com [user:Database:private] => sql24006_forward-limpet [pass:Database:private] => 80kO3&L%HedJv6eb^ [dbname:Database:private] => sql24006_events [dbh:Database:private] => PDO Object ( ) [stmt:Database:private] => PDOStatement Object ( [queryString] => SELECT m.*, u.name as from_user_name, u.email as from_user_email, s.name as show_title, s.location as show_location FROM messages m LEFT JOIN users u ON m.from_user_id = u.id LEFT JOIN shows s ON m.show_id = s.id WHERE m.to_user_id = ? AND m.is_archived = 0 ORDER BY m.created_at DESC LIMIT ? OFFSET ? ) [error:Database:private] => ) [notificationModel:UnifiedMessageModel:private] => NotificationModel Object ( [db:NotificationModel:private] => Database Object ( [host:Database:private] => ny-mysql01.offloadsql.com [user:Database:private] => sql24006_forward-limpet [pass:Database:private] => 80kO3&L%HedJv6eb^ [dbname:Database:private] => sql24006_events [dbh:Database:private] => PDO Object ( ) [stmt:Database:private] => [error:Database:private] => ) ) [ticketService:UnifiedMessageModel:private] => EnhancedTicketService Object ( [db:EnhancedTicketService:private] => Database Object ( [host:Database:private] => ny-mysql01.offloadsql.com [user:Database:private] => sql24006_forward-limpet [pass:Database:private] => 80kO3&L%HedJv6eb^ [dbname:Database:private] => sql24006_events [dbh:Database:private] => PDO Object ( ) [stmt:Database:private] => [error:Database:private] => ) ) ) [headerMessages] => Array ( [0] => stdClass Object ( [id] => 504 [from_user_id] => 3 [to_user_id] => 3 [subject] => Re: test message from registration [message] => testing reply from admin [show_id] => 9 [parent_message_id] => 503 [message_type] => direct [priority] => normal [is_read] => 1 [is_archived] => 0 [requires_reply] => 0 [allows_reply] => 1 [reply_used] => 0 [created_at] => 2025-07-19 01:04:47 [read_at] => 2025-07-19 01:04:48 [replied_at] => [ticket_number] => [email_message_id] => [original_sender_email] => [folder_id] => [owned_by_admin_id] => [security_token] => [from_user_name] => Brian Correll [from_user_email] => <EMAIL> [show_title] => Summer Classic Auto Show 2025 [show_location] => Rowan Sheriff Dept ) [1] => stdClass Object ( [id] => 503 [from_user_id] => 3 [to_user_id] => 3 [subject] => test message from registration [message] => testing registration messages [reply] [show_id] => 9 [parent_message_id] => [message_type] => admin [priority] => normal [is_read] => 1 [is_archived] => 0 [requires_reply] => 1 [allows_reply] => 1 [reply_used] => 0 [created_at] => 2025-07-19 01:04:24 [read_at] => 2025-07-19 22:06:45 [replied_at] => [ticket_number] => [email_message_id] => [original_sender_email] => [folder_id] => [owned_by_admin_id] => [security_token] => [from_user_name] => Brian Correll [from_user_email] => <EMAIL> [show_title] => Summer Classic Auto Show 2025 [show_location] => Rowan Sheriff Dept ) [2] => stdClass Object ( [id] => 502 [from_user_id] => 3 [to_user_id] => 3 [subject] => Re: show question [RER-2025-001-NFQKS9] [message] => ------sinikael-?=_1-17528869465410.7023045675010756 Content-Type: text/plain; charset=utf-8 Content-Transfer-Encoding: 7bit test reply Hello, Thank you for your inquiry about our events. Here is the information you requested: [Please add specific event details here] If you have any other questions, please do not hesitate to ask. Best regards, Admin Events and Shows Platform ------sinikael-?=_1-17528869465410.7023045675010756 Content-Type: text/html; charset=utf-8 Content-Transfer-Encoding: quoted-printable <html><head></head><body><div> <meta charset="utf-8"/> <div id="compose-body-wrapper" dir="auto"><div dir="auto">test reply </div><div dir="auto"><br/></div><div dir="auto" id="tmjah_g_1299">Get <a href="https://bluemail.me/download/" target="_blank">BlueMail for Mobile</a></div></div><div class="replyHeader" dir="auto">On July 18, 2025, at 21:01, Rowan Elite Rides Events and Shows &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt; wrote:<br/></div><br/><br/><div><blockquote cite="mid:<EMAIL>" type="cite" style="margin:0 0 0 .8ex;border-left:1px #ccc solid;padding-left:1ex">Hello, Thank you for your inquiry about our events. Here is the information you requested: [Please add specific event details here] If you have any other questions, please do not hesitate to ask. Best regards, Admin Events and Shows Platform </blockquote></div> </div></body></html> ------sinikael-?=_1-17528869465410.7023045675010756-- [show_id] => 5 [parent_message_id] => 500 [message_type] => email [priority] => normal [is_read] => 1 [is_archived] => 0 [requires_reply] => 0 [allows_reply] => 1 [reply_used] => 0 [created_at] => 2025-07-19 01:02:39 [read_at] => 2025-07-21 00:42:35 [replied_at] => [ticket_number] => RER-2025-001 [email_message_id] => <<EMAIL>> [original_sender_email] => <EMAIL> [folder_id] => 1 [owned_by_admin_id] => [security_token] => NFQKS9 [from_user_name] => Brian Correll [from_user_email] => <EMAIL> [show_title] => Name of show [show_location] => Heroes Car Wash ) [3] => stdClass Object ( [id] => 501 [from_user_id] => 3 [to_user_id] => 3 [subject] => Re: show question [RER-2025-001-NFQKS9] [message] => Hello, Thank you for your inquiry about our events. Here is the information you requested: [Please add specific event details here] If you have any other questions, please do not hesitate to ask. Best regards, Admin Events and Shows Platform [show_id] => 5 [parent_message_id] => 500 [message_type] => email [priority] => normal [is_read] => 1 [is_archived] => 0 [requires_reply] => 0 [allows_reply] => 1 [reply_used] => 0 [created_at] => 2025-07-19 01:01:48 [read_at] => 2025-07-19 01:01:51 [replied_at] => [ticket_number] => RER-2025-001 [email_message_id] => [original_sender_email] => [folder_id] => [owned_by_admin_id] => [security_token] => NFQKS9 [from_user_name] => Brian Correll [from_user_email] => <EMAIL> [show_title] => Name of show [show_location] => Heroes Car Wash ) [4] => stdClass Object ( [id] => 500 [from_user_id] => 3 [to_user_id] => 3 [subject] => show question [RER-2025-001-NFQKS9] [message] => ------sinikael-?=_1-17528868592260.1560288133272248 Content-Type: text/plain; charset=utf-8 Content-Transfer-Encoding: 7bit test message asking a question ------sinikael-?=_1-17528868592260.1560288133272248 Content-Type: text/html; charset=utf-8 Content-Transfer-Encoding: quoted-printable <html><head></head><body><div> <div dir="auto" id="compose-body-wrapper">test message </div><div dir="auto" id="compose-body-wrapper">asking a question</div><div dir="auto" id="compose-body-wrapper"><br/> <div dir="auto"><br/></div> <div dir="auto" id="tmjah_g_1299"> Get <a href="https://bluemail.me/download/" target="_blank">BlueMail for Mobile</a> </div> <div dir="auto"><br/></div> </div> </div></body></html> ------sinikael-?=_1-17528868592260.1560288133272248-- [show_id] => 5 [parent_message_id] => [message_type] => email [priority] => normal [is_read] => 1 [is_archived] => 0 [requires_reply] => 0 [allows_reply] => 1 [reply_used] => 0 [created_at] => 2025-07-19 01:01:18 [read_at] => 2025-07-19 01:01:51 [replied_at] => [ticket_number] => RER-2025-001 [email_message_id] => <<EMAIL>> [original_sender_email] => <EMAIL> [folder_id] => 1 [owned_by_admin_id] => [security_token] => NFQKS9 [from_user_name] => Brian Correll [from_user_email] => <EMAIL> [show_title] => Name of show [show_location] => Heroes Car Wash ) ) [headerMessage] => stdClass Object ( [id] => 500 [from_user_id] => 3 [to_user_id] => 3 [subject] => show question [RER-2025-001-NFQKS9] [message] => ------sinikael-?=_1-17528868592260.1560288133272248 Content-Type: text/plain; charset=utf-8 Content-Transfer-Encoding: 7bit test message asking a question ------sinikael-?=_1-17528868592260.1560288133272248 Content-Type: text/html; charset=utf-8 Content-Transfer-Encoding: quoted-printable <html><head></head><body><div> <div dir="auto" id="compose-body-wrapper">test message </div><div dir="auto" id="compose-body-wrapper">asking a question</div><div dir="auto" id="compose-body-wrapper"><br/> <div dir="auto"><br/></div> <div dir="auto" id="tmjah_g_1299"> Get <a href="https://bluemail.me/download/" target="_blank">BlueMail for Mobile</a> </div> <div dir="auto"><br/></div> </div> </div></body></html> ------sinikael-?=_1-17528868592260.1560288133272248-- [show_id] => 5 [parent_message_id] => [message_type] => email [priority] => normal [is_read] => 1 [is_archived] => 0 [requires_reply] => 0 [allows_reply] => 1 [reply_used] => 0 [created_at] => 2025-07-19 01:01:18 [read_at] => 2025-07-19 01:01:51 [replied_at] => [ticket_number] => RER-2025-001 [email_message_id] => <<EMAIL>> [original_sender_email] => <EMAIL> [folder_id] => 1 [owned_by_admin_id] => [security_token] => NFQKS9 [from_user_name] => Brian Correll [from_user_email] => <EMAIL> [show_title] => Name of show [show_location] => Heroes Car Wash ) [imageUrl] => https://events.rowaneliterides.com/uploads/users/user_3_688a36de12b1e.jpg [userName] => Brian Correll [urlParts] => Array ( [0] => image_editor [1] => upload [2] => event_photo [3] => 5 ) [baseUrl] => https://events.rowaneliterides.com [breadcrumbMappings] => Array ( [home] => Array ( [icon] => fa-home [label] => Home [url] => / ) [user] => Array ( [dashboard] => Array ( [icon] => fa-tachometer-alt [label] => Member Dashboard [url] => /user/dashboard ) [profile] => Array ( [icon] => fa-user [label] => Profile [url] => /user/profile ) [vehicles] => Array ( [icon] => fa-car [label] => My Vehicles [url] => /user/vehicles ) [registrations] => Array ( [icon] => fa-list [label] => My Registrations [url] => /user/registrations ) [createShow] => Array ( [icon] => fa-plus-circle [label] => Create Show [url] => /user/createShow ) ) [admin] => Array ( [dashboard] => Array ( [icon] => fa-crown [label] => Admin Dashboard [url] => /admin/dashboard ) [users] => Array ( [icon] => fa-users [label] => User Management [url] => /admin/users ) [shows] => Array ( [icon] => fa-calendar-check [label] => Show Management [url] => /admin/shows ) [settings] => Array ( [icon] => fa-cog [label] => Admin Settings [url] => /admin/settings ) [editShow] => Array ( [icon] => fa-edit [label] => Edit Show [url] => ) [viewRegistration] => Array ( [icon] => fa-eye [label] => View Registration [url] => ) ) [coordinator] => Array ( [dashboard] => Array ( [icon] => fa-clipboard-list [label] => Coordinator Dashboard [url] => /coordinator/dashboard ) [shows] => Array ( [icon] => fa-calendar-check [label] => My Shows [url] => /coordinator/shows ) [editShow] => Array ( [icon] => fa-edit [label] => Edit Show [url] => ) [registrations] => Array ( [icon] => fa-list [label] => Registrations [url] => ) ) [judge] => Array ( [dashboard] => Array ( [icon] => fa-gavel [label] => Judge Dashboard [url] => /judge/dashboard ) [assignments] => Array ( [icon] => fa-tasks [label] => Assignments [url] => /judge/assignments ) [score] => Array ( [icon] => fa-star [label] => Score Vehicle [url] => ) ) [staff] => Array ( [dashboard] => Array ( [icon] => fa-users [label] => Staff Dashboard [url] => /staff/dashboard ) [shows] => Array ( [icon] => fa-calendar [label] => Assigned Shows [url] => /staff/shows ) [registrations] => Array ( [icon] => fa-list [label] => Registrations [url] => ) ) [show] => Array ( [index] => Array ( [icon] => fa-search [label] => Browse Shows [url] => /show ) [view] => Array ( [icon] => fa-eye [label] => Show Details [url] => ) [register] => Array ( [icon] => fa-user-plus [label] => Register [url] => ) ) [calendar] => Array ( [index] => Array ( [icon] => fa-calendar [label] => Event Calendar [url] => /calendar ) [createEvent] => Array ( [icon] => fa-plus-circle [label] => Create Event [url] => /calendar/createEvent ) [event] => Array ( [icon] => fa-eye [label] => Event Details [url] => ) ) [notification_center] => Array ( [index] => Array ( [icon] => fa-bell [label] => Messages [url] => /notification_center ) ) [registration] => Array ( [index] => Array ( [icon] => fa-user-plus [label] => Registration [url] => /registration ) [register] => Array ( [icon] => fa-car [label] => Register Vehicle [url] => ) ) [auth] => Array ( [login] => Array ( [icon] => fa-sign-in-alt [label] => Login [url] => /auth/login ) [register] => Array ( [icon] => fa-user-plus [label] => Sign Up [url] => /auth/register ) [logout] => Array ( [icon] => fa-sign-out-alt [label] => Logout [url] => ) ) [progressive_auth] => Array ( [register] => Array ( [icon] => fa-user-plus [label] => Create Account [url] => /progressive_auth/register ) ) ) [breadcrumbs] => Array ( [0] => Array ( [icon] => fa-home [label] => Home [url] => https://events.rowaneliterides.com [active] => ) [1] => Array ( [icon] => fa-file [label] => Upload [url] => [active] => ) [2] => Array ( [icon] => fa-file [label] => Event photo [url] => [active] => ) [3] => Array ( [icon] => fa-eye [label] => Details [url] => [active] => 1 ) ) [currentPath] => /image_editor/upload/event_photo/5 [i] => 4 [part] => 5 [isLast] => 1 [genericMappings] => Array ( [home] => Array ( [icon] => fa-home [label] => Home ) [user] => Array ( [icon] => fa-user [label] => Member Area ) [admin] => Array ( [icon] => fa-crown [label] => Administration ) [coordinator] => Array ( [icon] => fa-clipboard-list [label] => Coordinator Area ) [judge] => Array ( [icon] => fa-gavel [label] => Judge Area ) [staff] => Array ( [icon] => fa-users [label] => Staff Area ) [show] => Array ( [icon] => fa-car [label] => Car Shows ) [calendar] => Array ( [icon] => fa-calendar [label] => Events ) [notification_center] => Array ( [icon] => fa-bell [label] => Messages ) [registration] => Array ( [icon] => fa-user-plus [label] => Registration ) [auth] => Array ( [icon] => fa-sign-in-alt [label] => Authentication ) ) [currentRole] => admin [roleDescriptions] => Array ( [user] => <strong>Member Capabilities:</strong><br>• Register vehicles for shows<br>• View event calendars<br>• Manage personal profile<br>• Submit vehicle photos<br>• Receive notifications [staff] => <strong>Staff Capabilities:</strong><br>• Check-in vehicles at shows<br>• Assist with registration<br>• Access staff dashboard<br>• View show reports<br>• Help coordinate events [judge] => <strong>Judge Capabilities:</strong><br>• Score vehicles in assigned categories<br>• Access mobile judging interface<br>• View judging assignments<br>• Submit scores and notes<br>• Access judge dashboard [coordinator] => <strong>Coordinator Capabilities:</strong><br>• Create and manage shows<br>• Set up judging categories<br>• Manage registrations<br>• Assign judges and staff<br>• Generate reports [admin] => <strong>Administrator Capabilities:</strong><br>• Full system access<br>• Manage all users and shows<br>• Access system settings<br>• View all dashboards<br>• Generate system reports<br>• All role capabilities ) [roleConfig] => Array ( [user] => Array ( [label] => Member [icon] => fa-user-circle ) [staff] => Array ( [label] => Staff [icon] => fa-users ) [judge] => Array ( [label] => Judge [icon] => fa-gavel ) [coordinator] => Array ( [label] => Coordinator [icon] => fa-clipboard-list ) [admin] => Array ( [label] => Administrator [icon] => fa-crown ) ) [roleInfo] => Array ( [label] => Administrator [icon] => fa-crown ) [crumb] => Array ( [icon] => fa-eye [label] => Details [url] => [active] => 1 ) [index] => 3 )