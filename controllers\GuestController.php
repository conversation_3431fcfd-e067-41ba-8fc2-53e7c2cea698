<?php
/**
 * Guest Controller
 * 
 * Handles guest-specific pages and conversion flows
 */
class GuestController extends Controller {
    
    /**
     * Constructor
     */
    public function __construct() {
        // No authentication required for guest pages
    }
    
    /**
     * Host conversion page - explains what guests are missing when they try to host a show
     */
    public function hostConversion() {
        // If user is already logged in, redirect to create show
        if (isset($_SESSION['user_id'])) {
            $this->redirect('user/createShow');
            return;
        }
        
        $data = [
            'title' => 'Host Your Own Car Show - FREE',
            'pageDescription' => 'Discover what you\'re missing! Host amazing car shows with QR code judging, fan voting, and real-time results. 100% FREE platform used by 1,200+ coordinators nationwide.',
            'pageKeywords' => 'host car show, car show coordinator, free car show platform, QR code judging, fan voting, automotive events'
        ];
        
        $this->view('guest/host-conversion', $data);
    }
    
    /**
     * Event creation conversion page - explains what guests are missing when they try to create events
     */
    public function eventConversion() {
        // If user is already logged in, redirect to create event
        if (isset($_SESSION['user_id'])) {
            $this->redirect('calendar/createEvent');
            return;
        }
        
        $data = [
            'title' => 'Create Your Own Events - FREE',
            'pageDescription' => 'Join thousands creating automotive events nationwide. Simple event creation, calendar integration, and community engagement tools - all FREE!',
            'pageKeywords' => 'create car event, automotive calendar, car meet, cruise night, car gathering'
        ];
        
        $this->view('guest/event-conversion', $data);
    }
    
    /**
     * Map access conversion page - explains what guests are missing when they try to access map
     */
    public function mapConversion() {
        // If user is already logged in, redirect to map
        if (isset($_SESSION['user_id'])) {
            $this->redirect('calendar/map');
            return;
        }
        
        $data = [
            'title' => 'Discover Events Near You - FREE',
            'pageDescription' => 'Find car shows and automotive events on an interactive map. Get directions, save favorites, and never miss an event near you!',
            'pageKeywords' => 'car shows near me, automotive events map, car meet finder, local car shows'
        ];
        
        $this->view('guest/map-conversion', $data);
    }
}
?>
