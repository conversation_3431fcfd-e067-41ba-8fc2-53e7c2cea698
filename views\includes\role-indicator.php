<?php
/**
 * Role Indicator Component
 * Displays user's current role with capabilities tooltip
 */

// Define role colors and icons
$roleConfig = [
    'guest' => [
        'color' => '#01579b',
        'icon' => 'fa-user',
        'label' => 'Guest',
        'capabilities' => [
            'Browse public events and shows',
            'View public calendar',
            'Access social sharing features',
            'Vote in fan favorites (via QR)'
        ]
    ],
    'user' => [
        'color' => '#4a148c',
        'icon' => 'fa-user-circle',
        'label' => 'Member',
        'capabilities' => [
            'Register vehicles',
            'Register for shows',
            'Create and host shows',
            'Access unified messaging',
            'Upload vehicle photos'
        ]
    ],
    'staff' => [
        'color' => '#1b5e20',
        'icon' => 'fa-users',
        'label' => 'Staff',
        'capabilities' => [
            'Manage assigned show registrations',
            'Check-in vehicles',
            'Create registrations for users',
            'Access staff reports',
            'All member capabilities'
        ]
    ],
    'judge' => [
        'color' => '#e65100',
        'icon' => 'fa-gavel',
        'label' => 'Judge',
        'capabilities' => [
            'Score vehicles in assigned categories',
            'Submit judging results',
            'Access judging reports',
            'View judge assignments',
            'All member capabilities'
        ]
    ],
    'coordinator' => [
        'color' => '#880e4f',
        'icon' => 'fa-clipboard-list',
        'label' => 'Coordinator',
        'capabilities' => [
            'Create and manage shows',
            'Assign judges and staff',
            'Manage show categories',
            'Handle payment settings',
            'Create calendar events',
            'All member capabilities'
        ]
    ],
    'admin' => [
        'color' => '#b71c1c',
        'icon' => 'fa-crown',
        'label' => 'Administrator',
        'capabilities' => [
            'Full system access',
            'Manage all users and shows',
            'Access system settings',
            'View all dashboards',
            'Generate system reports',
            'All role capabilities'
        ]
    ]
];

// Get current user role
$currentRole = $_SESSION['user_role'] ?? 'guest';
$roleInfo = $roleConfig[$currentRole] ?? $roleConfig['guest'];

// Check if user just gained new role (for celebration animation)
$showRoleUpgrade = false;
if (isset($_SESSION['role_just_upgraded']) && $_SESSION['role_just_upgraded']) {
    $showRoleUpgrade = true;
    unset($_SESSION['role_just_upgraded']); // Clear flag
}
?>

<div class="role-indicator-container">
    <div class="role-badge <?php echo $showRoleUpgrade ? 'role-upgraded' : ''; ?>" 
         style="background-color: <?php echo $roleInfo['color']; ?>;"
         data-bs-toggle="tooltip" 
         data-bs-placement="bottom" 
         data-bs-html="true"
         title="<strong><?php echo $roleInfo['label']; ?> Capabilities:</strong><br><?php echo implode('<br>• ', $roleInfo['capabilities']); ?>">
        
        <i class="fas <?php echo $roleInfo['icon']; ?>"></i>
        <span class="role-text"><?php echo $roleInfo['label']; ?></span>
        
        <?php if ($showRoleUpgrade): ?>
            <div class="upgrade-celebration">
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
            </div>
        <?php endif; ?>
    </div>
    
    <?php if ($currentRole === 'admin'): ?>
        <!-- Role switcher for admins -->
        <div class="role-switcher dropdown">
            <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-exchange-alt"></i>
            </button>
            <ul class="dropdown-menu">
                <li><h6 class="dropdown-header">Switch View</h6></li>
                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/user/dashboard">
                    <i class="fas fa-user-circle text-purple"></i> Member View
                </a></li>
                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/dashboard">
                    <i class="fas fa-clipboard-list text-pink"></i> Coordinator View
                </a></li>
                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/judge/dashboard">
                    <i class="fas fa-gavel text-orange"></i> Judge View
                </a></li>
                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/staff/dashboard">
                    <i class="fas fa-users text-green"></i> Staff View
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/dashboard">
                    <i class="fas fa-crown text-red"></i> Administrator View
                </a></li>
            </ul>
        </div>
    <?php endif; ?>
</div>

<!-- Role Indicator Styles -->
<style>
.role-indicator-container {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-right: 10px;
}

.role-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 15px;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    cursor: help;
    position: relative;
    overflow: hidden;
}

.role-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.role-badge i {
    font-size: 0.85rem;
}

.role-text {
    font-family: 'Orbitron', monospace;
}

/* Role upgrade celebration animation */
.role-badge.role-upgraded {
    animation: roleUpgrade 2s ease-in-out;
}

.upgrade-celebration {
    position: absolute;
    top: -10px;
    right: -10px;
    display: flex;
    gap: 2px;
}

.upgrade-celebration i {
    color: #FFD700;
    font-size: 0.7rem;
    animation: starTwinkle 1.5s ease-in-out infinite;
}

.upgrade-celebration i:nth-child(2) {
    animation-delay: 0.2s;
}

.upgrade-celebration i:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes roleUpgrade {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); box-shadow: 0 0 20px rgba(255, 215, 0, 0.6); }
    100% { transform: scale(1); }
}

@keyframes starTwinkle {
    0%, 100% { opacity: 0.3; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
}

/* Role switcher styling */
.role-switcher .btn {
    border-color: rgba(255,255,255,0.3);
    color: rgba(255,255,255,0.8);
    padding: 2px 6px;
    font-size: 0.75rem;
}

.role-switcher .btn:hover {
    background-color: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

/* Role color classes for switcher */
.text-purple { color: #4a148c !important; }
.text-pink { color: #880e4f !important; }
.text-orange { color: #e65100 !important; }
.text-green { color: #1b5e20 !important; }
.text-red { color: #b71c1c !important; }

/* Mobile responsive */
@media (max-width: 768px) {
    .role-indicator-container {
        margin-left: 8px;
    }
    
    .role-badge {
        padding: 4px 8px;
        font-size: 0.75rem;
    }
    
    .role-text {
        display: none; /* Show only icon on mobile */
    }
    
    .role-switcher {
        display: none; /* Hide switcher on mobile */
    }
}

/* Tooltip styling */
.tooltip-inner {
    max-width: 250px;
    text-align: left;
    background-color: rgba(0,0,0,0.9);
    border-radius: 8px;
    padding: 10px;
}
</style>

<!-- Initialize tooltips -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips for role indicators
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Show role upgrade celebration if applicable
    <?php if ($showRoleUpgrade): ?>
    setTimeout(function() {
        // Show toast notification for role upgrade
        if (typeof showToast === 'function') {
            showToast('🎉 Congratulations! You are now a <?php echo $roleInfo['label']; ?>!', 'success', 5000);
        }
    }, 500);
    <?php endif; ?>
});
</script>
