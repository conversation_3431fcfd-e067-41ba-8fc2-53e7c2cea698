<?php
require APPROOT . '/views/includes/header.php';
require_once APPROOT . '/models/SettingsModel.php';
?>

<style>
    .text-purple {
        color: #6f42c1 !important;
    }
    .bg-purple {
        background-color: #6f42c1 !important;
    }
</style>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">System Settings</h1>
            <p class="text-muted">Manage your application settings and configurations</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>

    <?php if (isset($success) && $success) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> System settings have been updated.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Settings Dashboard -->
    <div class="row g-4 mb-5">

        <!-- Email Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-danger bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-envelope text-danger fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Email Settings</h4>
                    </div>
                    <p class="card-text text-muted">Configure SMTP server, email templates, and notification settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_email" class="stretched-link text-decoration-none">
                        <span class="d-none">View Email Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Notification System Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-bell text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Notifications</h4>
                    </div>
                    <p class="card-text text-muted">Manage notification settings, SMS providers, queue, testing, and installation.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_notifications" class="stretched-link text-decoration-none">
                        <span class="d-none">View Notification Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- QR Code Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-qrcode text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">QR Codes</h4>
                    </div>
                    <p class="card-text text-muted">Configure QR code settings, analytics, and generation options.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_qr_codes" class="stretched-link text-decoration-none">
                        <span class="d-none">View QR Code Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Judging & Metrics Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-gavel text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Judging & Metrics</h4>
                    </div>
                    <p class="card-text text-muted">Manage judging metrics, categories, and age weights for shows.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_judging" class="stretched-link text-decoration-none">
                        <span class="d-none">View Judging Settings</span>
                    </a>
                </div>
            </div>
        </div>



        <!-- Payment Methods Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-secondary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-credit-card text-secondary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Payment Methods</h4>
                    </div>
                    <p class="card-text text-muted">Configure payment gateways, methods, and transaction settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_payment" class="stretched-link text-decoration-none">
                        <span class="d-none">View Payment Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Forms & Fields Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-dark bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-shield-alt text-dark fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Forms & Fields</h4>
                    </div>
                    <p class="card-text text-muted">Manage System Forms, Fields, and Templates.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_forms" class="stretched-link text-decoration-none">
                        <span class="d-none">View Forms & Fields</span>
                    </a>
                </div>
            </div>
        </div>



        <!-- Media & Appearance Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-images text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Media & Appearance</h4>
                    </div>
                    <p class="card-text text-muted">Configure image settings, optimization, and storage options.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_media" class="stretched-link text-decoration-none">
                        <span class="d-none">View Media & Appearance</span>
                    </a>
                </div>
            </div>
        </div>



        <!-- Listing Fee Management Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-ticket-alt text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Listing Fees</h4>
                    </div>
                    <p class="card-text text-muted">Manage listing fees, discount codes, and pre-approved coordinators.</p>
                    <a href="<?php echo BASE_URL; ?>/adminListing" class="stretched-link text-decoration-none">
                        <span class="d-none">View Listing Fee Management</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Calendar & Map Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-calendar-alt text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Calendar & Map</h4>
                    </div>
                    <p class="card-text text-muted">Configure calendar display options, map provider settings, and geocode event locations.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_calendar" class="stretched-link text-decoration-none">
                        <span class="d-none">View Calendar & Map Settings</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Club Ownership Verification Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-shield-alt text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Club Ownership</h4>
                        <?php
                        // Get pending verification count
                        $pendingCount = 0;
                        try {
                            $calendarModel = new CalendarModel();
                            $stats = $calendarModel->getVerificationStatistics();
                            $pendingCount = $stats['pending'] ?? 0;
                        } catch (Exception $e) {
                            // Silently handle error
                        }
                        ?>
                        <?php if ($pendingCount > 0): ?>
                            <span class="badge bg-danger ms-2">
                                <?php echo $pendingCount; ?> pending
                            </span>
                        <?php endif; ?>
                    </div>
                    <p class="card-text text-muted">Review and manage club ownership verification requests from users.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_club_ownership" class="stretched-link text-decoration-none">
                        <span class="d-none">View Club Ownership Settings</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Email Server Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-server text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Email Server</h4>
                        <?php
                        // Check if email processing is enabled
                        $settingsModel = new SettingsModel();
                        $emailEnabled = $settingsModel->getSetting('email_processing_enabled', '0') === '1';
                        ?>
                        <?php if ($emailEnabled): ?>
                            <span class="badge bg-success ms-2">Active</span>
                        <?php else: ?>
                            <span class="badge bg-secondary ms-2">Inactive</span>
                        <?php endif; ?>
                    </div>
                    <p class="card-text text-muted">Configure POP3/IMAP email retrieval and processing settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_email_server" class="stretched-link text-decoration-none">
                        <span class="d-none">View Email Server Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Email Templates Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-envelope-open-text text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Email Templates</h4>
                    </div>
                    <p class="card-text text-muted">Manage email templates for auto-replies and quick responses.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_email_templates" class="stretched-link text-decoration-none">
                        <span class="d-none">View Email Templates</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Email Dashboard Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-envelope-open-text text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Email Dashboard</h4>
                        <?php
                        // Get unread email count for current admin
                        if (isset($_SESSION['user_id'])) {
                            $db = new Database();
                            $db->query("SELECT COUNT(*) as count FROM messages WHERE to_user_id = :user_id AND is_read = 0 AND message_type = 'email'");
                            $db->bind(':user_id', $_SESSION['user_id']);
                            $unreadCount = $db->single()->count ?? 0;

                            if ($unreadCount > 0): ?>
                                <span class="badge bg-danger ms-2"><?= $unreadCount ?> unread</span>
                            <?php endif;
                        } ?>
                    </div>
                    <p class="card-text text-muted">Manage incoming emails with folders, reminders, and advanced sorting.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/email_dashboard" class="stretched-link text-decoration-none">
                        <span class="d-none">View Email Dashboard</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Developer Tools Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-purple bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-code text-purple fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Developer Tools</h4>
                    </div>
                    <p class="card-text text-muted">Advanced tools for system administration and development.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_developer" class="stretched-link text-decoration-none">
                        <span class="d-none">View Developer Tools</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white py-3">
                    <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> System Information</h3>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-laptop-code me-2"></i> Application</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <tbody>
                                        <tr>
                                            <th scope="row" style="width: 40%">Version</th>
                                            <td><span class="badge bg-primary"><?php echo APP_VERSION; ?></span></td>
                                        </tr>
                                        <tr>
                                            <th scope="row">Install Date</th>
                                            <td><?php echo formatDateTimeForUser(INSTALL_DATE, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></td>
                                        </tr>
                                        <tr>
                                            <th scope="row">Base URL</th>
                                            <td><code><?php echo BASE_URL; ?></code></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-server me-2"></i> Server</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <tbody>
                                        <tr>
                                            <th scope="row" style="width: 40%">PHP Version</th>
                                            <td><span class="badge bg-success"><?php echo phpversion(); ?></span></td>
                                        </tr>
                                        <tr>
                                            <th scope="row">Server Software</th>
                                            <td><?php echo $_SERVER['SERVER_SOFTWARE']; ?></td>
                                        </tr>
                                        <tr>
                                            <th scope="row">Database</th>
                                            <td><code><?php echo DB_HOST . ' / ' . DB_NAME; ?></code></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Maintenance Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-warning text-dark py-3">
                    <h3 class="card-title mb-0"><i class="fas fa-tools me-2"></i> Maintenance</h3>
                </div>
                <div class="card-body p-4">
                    <div class="row mb-4">
                        <div class="col-md-12 text-center">
                            <div class="d-grid d-md-block">
                                <button type="button" class="btn btn-outline-success btn-lg p-3" data-bs-toggle="modal" data-bs-target="#backupModal">
                                    <i class="fas fa-download d-block mb-2 fa-2x mx-auto"></i>
                                    <span>Backup Database</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Warning</h5>
                                <p class="mb-0">Some maintenance actions are potentially destructive and should be used with caution. Always backup your database before making significant changes.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Backup Modal -->
<div class="modal fade" id="backupModal" tabindex="-1" aria-labelledby="backupModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="backupModalLabel"><i class="fas fa-database me-2"></i> Database Backup</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <i class="fas fa-download fa-4x text-success mb-3"></i>
                    <h4>Create Database Backup</h4>
                </div>
                <p>This will create a complete backup of your current database. The backup file will be available for download after creation.</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> Regular backups are recommended to prevent data loss.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?php echo BASE_URL; ?>/admin/backupDatabase" method="post">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-success">Create Backup</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    .hover-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .hover-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
    }
    
    .icon-box {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    @media (max-width: 767.98px) {
        .icon-box {
            width: 50px;
            height: 50px;
        }
        
        .icon-box i {
            font-size: 1.5rem !important;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // All button-specific functionality has been removed
    });
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>