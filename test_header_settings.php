<?php
/**
 * Test Header Settings Endpoint
 * This file tests if the header settings endpoint is working correctly
 */

// Include the application bootstrap
require_once 'config/config.php';
require_once 'core/App.php';
require_once 'core/Controller.php';
require_once 'core/Database.php';

// Test the endpoint directly
echo "<h2>Testing Header Settings Endpoint</h2>";

try {
    // Create HomeController instance
    require_once 'controllers/HomeController.php';
    $controller = new HomeController();
    
    echo "<h3>Testing getHeaderSettings method:</h3>";
    
    // Capture output
    ob_start();
    $controller->getHeaderSettings();
    $output = ob_get_clean();
    
    echo "<pre>";
    echo "Raw Output: " . htmlspecialchars($output) . "\n";
    
    // Try to decode JSON
    $data = json_decode($output, true);
    if ($data) {
        echo "\nDecoded JSON:\n";
        print_r($data);
        
        if (isset($data['success']) && $data['success']) {
            echo "\n✅ SUCCESS: Header settings loaded successfully\n";
            echo "Settings found:\n";
            foreach ($data['settings'] as $key => $value) {
                echo "  - {$key}: {$value}\n";
            }
        } else {
            echo "\n❌ ERROR: Response indicates failure\n";
        }
    } else {
        echo "\n❌ ERROR: Invalid JSON response\n";
        echo "JSON Error: " . json_last_error_msg() . "\n";
    }
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<pre>❌ EXCEPTION: " . htmlspecialchars($e->getMessage()) . "</pre>";
}

echo "<hr>";
echo "<p><strong>Instructions:</strong></p>";
echo "<ul>";
echo "<li>If you see 'SUCCESS' above, the endpoint is working correctly</li>";
echo "<li>All settings should show '0' (disabled) by default</li>";
echo "<li>You can delete this test file after confirming it works</li>";
echo "</ul>";
?>