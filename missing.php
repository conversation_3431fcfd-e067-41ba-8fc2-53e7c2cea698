<?php
/**
 * MISSING EVENT PHOTO ADMIN METHODS FOR AdminController.php
 * 
 * Copy these methods into AdminController.php before the closing brace }
 * These methods use integrated approach with existing $this->db connection
 * NO EventPhotoModel dependencies - completely self-contained
 */

    /**
     * Event Photo Location Settings (Integrated Approach)
     */
    public function event_photo_admin_location() {
        // Check admin access
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->setFlashMessage('error', 'Invalid security token');
                $this->redirect('admin/event_photo_admin_location');
                return;
            }
            
            try {
                // Get form data
                $settings = [
                    'require_gps_verification' => isset($_POST['require_gps_verification']) ? '1' : '0',
                    'location_radius_miles' => $_POST['location_radius_miles'] ?? '1.0',
                    'require_event_attendance' => isset($_POST['require_event_attendance']) ? '1' : '0',
                    'allow_manual_location' => isset($_POST['allow_manual_location']) ? '1' : '0',
                    'location_accuracy_threshold' => $_POST['location_accuracy_threshold'] ?? '100'
                ];
                
                // Update settings using integrated approach
                if ($this->updateEventPhotoAdminSettings($settings)) {
                    $this->setFlashMessage('success', 'Location settings updated successfully');
                } else {
                    $this->setFlashMessage('error', 'Failed to update settings');
                }
                
            } catch (Exception $e) {
                error_log("AdminController::event_photo_admin_location - Error: " . $e->getMessage());
                $this->setFlashMessage('error', 'An error occurred while updating settings');
            }
            
            $this->redirect('admin/event_photo_admin_location');
            return;
        }
        
        // Get current settings using integrated approach
        $settings = $this->getEventPhotoAdminSettings();
        
        $data = [
            'title' => 'Event Photo Location & Access Settings',
            'settings' => $settings,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/settings_event_photo_location', $data);
    }
    
    /**
     * Event Photo Storage Settings (Integrated Approach)
     */
    public function event_photo_admin_storage() {
        // Check admin access
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->setFlashMessage('error', 'Invalid security token');
                $this->redirect('admin/event_photo_admin_storage');
                return;
            }
            
            try {
                // Get form data
                $settings = [
                    'auto_cleanup_enabled' => isset($_POST['auto_cleanup_enabled']) ? '1' : '0',
                    'high_engagement_threshold' => $_POST['high_engagement_threshold'] ?? '10',
                    'medium_engagement_threshold' => $_POST['medium_engagement_threshold'] ?? '3',
                    'high_engagement_retention_months' => $_POST['high_engagement_retention_months'] ?? '24',
                    'medium_engagement_retention_months' => $_POST['medium_engagement_retention_months'] ?? '12',
                    'low_engagement_retention_months' => $_POST['low_engagement_retention_months'] ?? '6'
                ];
                
                // Update settings using integrated approach
                if ($this->updateEventPhotoAdminSettings($settings)) {
                    $this->setFlashMessage('success', 'Storage settings updated successfully');
                } else {
                    $this->setFlashMessage('error', 'Failed to update settings');
                }
                
            } catch (Exception $e) {
                error_log("AdminController::event_photo_admin_storage - Error: " . $e->getMessage());
                $this->setFlashMessage('error', 'An error occurred while updating settings');
            }
            
            $this->redirect('admin/event_photo_admin_storage');
            return;
        }
        
        // Get current settings using integrated approach
        $settings = $this->getEventPhotoAdminSettings();
        
        $data = [
            'title' => 'Event Photo Storage & Cleanup Settings',
            'settings' => $settings,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/settings_event_photo_storage', $data);
    }
    
    /**
     * Event Photo Moderation Settings (Integrated Approach)
     */
    public function event_photo_admin_moderation() {
        // Check admin access
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->setFlashMessage('error', 'Invalid security token');
                $this->redirect('admin/event_photo_admin_moderation');
                return;
            }
            
            try {
                // Get form data
                $settings = [
                    'moderation_enabled' => isset($_POST['moderation_enabled']) ? '1' : '0',
                    'enable_reporting' => isset($_POST['enable_reporting']) ? '1' : '0',
                    'auto_approve_trusted_users' => isset($_POST['auto_approve_trusted_users']) ? '1' : '0',
                    'require_approval_new_users' => isset($_POST['require_approval_new_users']) ? '1' : '0',
                    'auto_flag_inappropriate' => isset($_POST['auto_flag_inappropriate']) ? '1' : '0',
                    'moderation_email_notifications' => isset($_POST['moderation_email_notifications']) ? '1' : '0',
                    'reports_threshold' => $_POST['reports_threshold'] ?? '3'
                ];
                
                // Update settings using integrated approach
                if ($this->updateEventPhotoAdminSettings($settings)) {
                    $this->setFlashMessage('success', 'Moderation settings updated successfully');
                } else {
                    $this->setFlashMessage('error', 'Failed to update settings');
                }
                
            } catch (Exception $e) {
                error_log("AdminController::event_photo_admin_moderation - Error: " . $e->getMessage());
                $this->setFlashMessage('error', 'An error occurred while updating settings');
            }
            
            $this->redirect('admin/event_photo_admin_moderation');
            return;
        }
        
        // Get current settings using integrated approach
        $settings = $this->getEventPhotoAdminSettings();
        
        $data = [
            'title' => 'Event Photo Moderation Settings',
            'settings' => $settings,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('admin/settings_event_photo_moderation', $data);
    }

    /**
     * Get Event Photo Admin Settings (Integrated Helper Method)
     */
    private function getEventPhotoAdminSettings() {
        // Create settings table if it doesn't exist
        $this->createEventPhotoAdminTable();

        $this->db->query('SELECT setting_name, setting_value FROM event_photo_admin_settings');
        $results = $this->db->resultSet();

        $settings = [];
        foreach ($results as $setting) {
            $settings[$setting->setting_name] = $setting->setting_value;
        }

        // Return defaults if no settings exist
        if (empty($settings)) {
            return [
                'enabled' => 1,
                'require_gps_verification' => 1,
                'location_radius_miles' => 1.0,
                'time_buffer_hours_before' => 24,
                'time_buffer_hours_after' => 24,
                'max_photos_per_user_per_event' => 50,
                'max_file_size_mb' => 10,
                'photo_quality' => 85,
                'default_privacy_level' => 'public',
                'enable_facebook_sharing' => 1,
                'require_event_attendance' => 0,
                'allow_manual_location' => 0,
                'location_accuracy_threshold' => 100,
                'auto_cleanup_enabled' => 0,
                'high_engagement_threshold' => 10,
                'medium_engagement_threshold' => 3,
                'high_engagement_retention_months' => 24,
                'medium_engagement_retention_months' => 12,
                'low_engagement_retention_months' => 6,
                'moderation_enabled' => 0,
                'enable_reporting' => 1,
                'auto_approve_trusted_users' => 1,
                'require_approval_new_users' => 1,
                'auto_flag_inappropriate' => 0,
                'moderation_email_notifications' => 1,
                'reports_threshold' => 3
            ];
        }

        return $settings;
    }

    /**
     * Update Event Photo Admin Settings (Integrated Helper Method)
     */
    private function updateEventPhotoAdminSettings($settings) {
        try {
            foreach ($settings as $name => $value) {
                $this->db->query('INSERT INTO event_photo_admin_settings (setting_name, setting_value)
                                 VALUES (:name, :value)
                                 ON DUPLICATE KEY UPDATE setting_value = :value');
                $this->db->bind(':name', $name);
                $this->db->bind(':value', $value);
                $this->db->execute();
            }
            return true;
        } catch (Exception $e) {
            error_log('Error updating event photo admin settings: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create Event Photo Admin Settings Table (Integrated Helper Method)
     */
    private function createEventPhotoAdminTable() {
        try {
            $this->db->query('CREATE TABLE IF NOT EXISTS event_photo_admin_settings (
                id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
                setting_name VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT NOT NULL,
                description TEXT,
                setting_type ENUM("boolean", "integer", "decimal", "string", "text") DEFAULT "string",
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_admin_settings_name (setting_name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
            $this->db->execute();
        } catch (Exception $e) {
            error_log('Error creating event photo admin settings table: ' . $e->getMessage());
        }
    }
