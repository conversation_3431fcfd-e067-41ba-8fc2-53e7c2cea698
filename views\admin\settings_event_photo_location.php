<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Event Photo Location & Access Settings
                    </h1>
                    <p class="text-muted">Configure location verification, GPS settings, and access controls</p>
                </div>
                <div>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_event_photos" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Event Photos
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-globe me-2"></i>
                        Location & Access Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo BASE_URL; ?>/admin/event_photo_admin_location">
                        <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token'] ?? ''; ?>">
                        
                        <!-- GPS Verification -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-info mb-3">
                                    <i class="fas fa-satellite me-2"></i>
                                    GPS Verification
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                    <input class="form-check-input" type="checkbox" id="require_gps_verification" name="require_gps_verification"
                                           style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                           <?php echo ($data['settings']['require_gps_verification'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="require_gps_verification">
                                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                        <strong>Require GPS Verification</strong>
                                        <small class="d-block text-muted">Users must be physically at the event location to share photos</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="location_radius_miles" class="form-label">Location Verification Radius</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="location_radius_miles" name="location_radius_miles" 
                                           value="<?php echo $data['settings']['location_radius_miles'] ?? 1.0; ?>" 
                                           min="0.1" max="10" step="0.1" required>
                                    <span class="input-group-text">miles</span>
                                </div>
                                <small class="text-muted">How close users must be to the event location (0.1 - 10 miles)</small>
                            </div>
                        </div>

                        <!-- Time Windows -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-info mb-3">
                                    <i class="fas fa-clock me-2"></i>
                                    Time Windows
                                </h6>
                                <p class="text-muted">Control when users can share photos relative to event start/end times</p>
                            </div>
                            <div class="col-md-6">
                                <label for="time_buffer_hours_before" class="form-label">Hours Before Event</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="time_buffer_hours_before" name="time_buffer_hours_before" 
                                           value="<?php echo $data['settings']['time_buffer_hours_before'] ?? 24; ?>" 
                                           min="0" max="168" required>
                                    <span class="input-group-text">hours</span>
                                </div>
                                <small class="text-muted">How early users can start sharing photos (0 - 168 hours)</small>
                            </div>
                            <div class="col-md-6">
                                <label for="time_buffer_hours_after" class="form-label">Hours After Event</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="time_buffer_hours_after" name="time_buffer_hours_after" 
                                           value="<?php echo $data['settings']['time_buffer_hours_after'] ?? 24; ?>" 
                                           min="0" max="168" required>
                                    <span class="input-group-text">hours</span>
                                </div>
                                <small class="text-muted">How long after the event photos can be shared (0 - 168 hours)</small>
                            </div>
                        </div>

                        <!-- Access Controls -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-info mb-3">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Access Controls
                                </h6>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                    <input class="form-check-input" type="checkbox" id="require_event_attendance" name="require_event_attendance"
                                           style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                           <?php echo ($data['settings']['require_event_attendance'] ?? false) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="require_event_attendance">
                                        <i class="fas fa-user-check me-2 text-success"></i>
                                        <strong>Require Event Registration</strong>
                                        <small class="d-block text-muted">Only registered attendees can share photos</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                    <input class="form-check-input" type="checkbox" id="allow_manual_location" name="allow_manual_location"
                                           style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                           <?php echo ($data['settings']['allow_manual_location'] ?? false) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="allow_manual_location">
                                        <i class="fas fa-edit me-2 text-warning"></i>
                                        <strong>Allow Manual Location Entry</strong>
                                        <small class="d-block text-muted">Users can manually enter location if GPS fails</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Backup Location Methods -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-info mb-3">
                                    <i class="fas fa-backup me-2"></i>
                                    Backup Location Methods
                                </h6>
                                <p class="text-muted">Fallback methods when GPS is unavailable or disabled</p>
                            </div>
                            <div class="col-md-6">
                                <label for="backup_location_method" class="form-label">Backup Location Method</label>
                                <select class="form-select" id="backup_location_method" name="backup_location_method" required>
                                    <option value="ip_geolocation" <?php echo ($data['settings']['backup_location_method'] ?? 'ip_geolocation') === 'ip_geolocation' ? 'selected' : ''; ?>>
                                        IP Geolocation - Use user's IP address
                                    </option>
                                    <option value="manual_entry" <?php echo ($data['settings']['backup_location_method'] ?? 'ip_geolocation') === 'manual_entry' ? 'selected' : ''; ?>>
                                        Manual Entry - User enters location manually
                                    </option>
                                    <option value="disable_sharing" <?php echo ($data['settings']['backup_location_method'] ?? 'ip_geolocation') === 'disable_sharing' ? 'selected' : ''; ?>>
                                        Disable Sharing - Don't allow photo sharing without GPS
                                    </option>
                                    <option value="admin_approval" <?php echo ($data['settings']['backup_location_method'] ?? 'ip_geolocation') === 'admin_approval' ? 'selected' : ''; ?>>
                                        Admin Approval - Require admin approval for non-GPS photos
                                    </option>
                                </select>
                                <small class="text-muted">What to do when GPS verification is not available</small>
                            </div>
                        </div>

                        <!-- Location Accuracy Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Location Accuracy Information
                                    </h6>
                                    <ul class="mb-0">
                                        <li><strong>GPS Accuracy:</strong> Typically accurate within 3-5 meters outdoors</li>
                                        <li><strong>IP Geolocation:</strong> Accurate within 1-10 miles depending on ISP</li>
                                        <li><strong>Manual Entry:</strong> Relies on user honesty and knowledge</li>
                                        <li><strong>Radius Settings:</strong> Larger radius = more lenient, smaller = more strict</li>
                                        <li><strong>Indoor Events:</strong> GPS may be less accurate, consider larger radius</li>
                                        <li><strong>Time Buffers:</strong> Allow for setup/teardown and travel time</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <button type="submit" class="btn btn-info btn-lg text-white">
                                    <i class="fas fa-save me-2"></i>
                                    Save Location Settings
                                </button>
                                <a href="<?php echo BASE_URL; ?>/admin/settings_event_photos" class="btn btn-outline-secondary btn-lg ms-2">
                                    <i class="fas fa-times me-2"></i>
                                    Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-check-input:checked {
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.text-info {
    color: #17a2b8 !important;
}

.input-group-text {
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.form-control:focus, .form-select:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
