<?php
/**
 * Photo Share Controller
 * 
 * Handles individual photo sharing with proper Open Graph tags and fallbacks
 */

class PhotoShareController extends Controller {

    private $imageModel;
    private $db;

    public function __construct() {
        $this->imageModel = $this->model('ImageEditorModel');
        $this->db = new Database();
    }
    
    /**
     * Share individual photo
     * 
     * @param int $photoId Photo ID
     */
    public function photo($photoId = null) {
        if (!$photoId) {
            // Debug: Log when no photo ID is provided
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('PhotoShareController: No photo ID provided');
            }
            $this->redirect('home');
            return;
        }

        // Debug: Log the photo ID being requested
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('PhotoShareController: Requesting photo ID: ' . $photoId);
        }
        
        // Get photo details with event information
        $this->db->query('SELECT i.*, epm.event_type, epm.event_id, epm.category, epm.caption, epm.privacy_level,
                         CASE
                             WHEN epm.event_type = "event" THEN ce.title
                             WHEN epm.event_type = "show" THEN s.name
                             ELSE "Event Photo"
                         END as event_name,
                         CASE
                             WHEN epm.event_type = "event" THEN ce.start_date
                             WHEN epm.event_type = "show" THEN s.start_date
                             ELSE NULL
                         END as event_date,
                         CASE
                             WHEN epm.event_type = "event" THEN ce.location
                             WHEN epm.event_type = "show" THEN s.location
                             ELSE NULL
                         END as event_location,
                         u.name as photographer_name
                         FROM images i
                         LEFT JOIN event_photo_metadata epm ON i.id = epm.image_id
                         LEFT JOIN calendar_events ce ON epm.event_type = "event" AND epm.event_id = ce.id
                         LEFT JOIN shows s ON epm.event_type = "show" AND epm.event_id = s.id
                         LEFT JOIN users u ON i.user_id = u.id
                         WHERE i.id = :photo_id AND i.entity_type = "event_photo"');
        $this->db->bind(':photo_id', $photoId);
        $photo = $this->db->single();

        // Debug: Log photo query result
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('PhotoShareController: Photo query result: ' . ($photo ? 'Found' : 'Not found'));
            if ($photo) {
                error_log('PhotoShareController: Photo file_path: ' . $photo->file_path);
                error_log('PhotoShareController: Photo data: ' . print_r($photo, true));
            }
        }

        if (!$photo) {
            // Photo not found - redirect to default gallery with message
            $this->handleMissingPhoto();
            return;
        }
        
        // Check privacy settings
        if (!$this->canViewPhoto($photo)) {
            $this->handlePrivatePhoto();
            return;
        }
        
        // Get admin-configured category labels
        $categoryLabels = $this->getEventPhotoCategoryLabels();
        $categoryLabel = $categoryLabels[$photo->category] ?? '📷 Photo';
        
        // Prepare photo data for view
        $photoData = [
            'id' => $photo->id,
            'filename' => $photo->filename ?? basename($photo->file_path),
            'full_url' => BASE_URL . '/' . $photo->file_path,
            'thumbnail_url' => BASE_URL . '/' . str_replace('.', '_thumb.', $photo->file_path),
            'caption' => $photo->caption ?? '',
            'category' => $photo->category,
            'category_label' => $categoryLabel,
            'event_name' => $photo->event_name,
            'event_date' => $photo->event_date,
            'event_location' => $photo->event_location ?? '',
            'event_type' => $photo->event_type ?? '',
            'event_id' => $photo->event_id ?? '',
            'photographer_name' => $photo->photographer_name ?? 'Anonymous',
            'created_at' => $photo->created_at,
            'privacy_level' => $photo->privacy_level ?? 'public'
        ];
        
        // Prepare Open Graph data - focused on the photo
        $ogData = [
            'title' => $this->generatePhotoTitle($photoData),
            'description' => $this->generatePhotoDescription($photoData),
            'image' => $photoData['full_url'],
            'url' => BASE_URL . '/photo/share/' . $photoId,
            'type' => 'photo'
        ];
        
        $data = [
            'title' => $ogData['title'],
            'pageTitle' => $ogData['title'],
            'pageDescription' => $ogData['description'],
            'pageImage' => $ogData['image'],
            'pageUrl' => $ogData['url'],
            'pageType' => $ogData['type'],
            'photo' => $photoData,
            'og_data' => $ogData
        ];
        
        $this->view('photo_share/simple', $data);
    }

    /**
     * Debug method to see what meta tags are being generated
     *
     * @param int $photoId Photo ID
     */
    public function debug($photoId = null) {
        if (!$photoId) {
            $this->redirect('home');
            return;
        }

        // Use the same logic as the photo method but show debug view
        $this->db->query('SELECT i.*, epm.event_type, epm.event_id, epm.category, epm.caption, epm.privacy_level,
                         CASE
                             WHEN epm.event_type = "event" THEN ce.title
                             WHEN epm.event_type = "show" THEN s.name
                             ELSE "Event Photo"
                         END as event_name,
                         CASE
                             WHEN epm.event_type = "event" THEN ce.start_date
                             WHEN epm.event_type = "show" THEN s.start_date
                             ELSE NULL
                         END as event_date,
                         CASE
                             WHEN epm.event_type = "event" THEN ce.location
                             WHEN epm.event_type = "show" THEN s.location
                             ELSE NULL
                         END as event_location,
                         u.name as photographer_name
                         FROM images i
                         LEFT JOIN event_photo_metadata epm ON i.id = epm.image_id
                         LEFT JOIN calendar_events ce ON epm.event_type = "event" AND epm.event_id = ce.id
                         LEFT JOIN shows s ON epm.event_type = "show" AND epm.event_id = s.id
                         LEFT JOIN users u ON i.user_id = u.id
                         WHERE i.id = :photo_id AND i.entity_type = "event_photo"');
        $this->db->bind(':photo_id', $photoId);
        $photo = $this->db->single();

        if (!$photo) {
            echo "Photo not found with ID: " . $photoId;
            return;
        }

        // Get admin-configured category labels
        $categoryLabels = $this->getEventPhotoCategoryLabels();
        $categoryLabel = $categoryLabels[$photo->category] ?? '📷 Photo';

        // Prepare photo data for view
        $photoData = [
            'id' => $photo->id,
            'filename' => $photo->filename ?? basename($photo->file_path),
            'full_url' => BASE_URL . '/' . $photo->file_path,
            'thumbnail_url' => BASE_URL . '/' . str_replace('.', '_thumb.', $photo->file_path),
            'caption' => $photo->caption ?? '',
            'category' => $photo->category,
            'category_label' => $categoryLabel,
            'event_name' => $photo->event_name,
            'event_date' => $photo->event_date,
            'event_location' => $photo->event_location ?? '',
            'event_type' => $photo->event_type ?? '',
            'event_id' => $photo->event_id ?? '',
            'photographer_name' => $photo->photographer_name ?? 'Anonymous',
            'created_at' => $photo->created_at,
            'privacy_level' => $photo->privacy_level ?? 'public'
        ];

        // Prepare Open Graph data - focused on the photo
        $ogData = [
            'title' => $this->generatePhotoTitle($photoData),
            'description' => $this->generatePhotoDescription($photoData),
            'image' => $photoData['full_url'],
            'url' => BASE_URL . '/photo/share/' . $photoId,
            'type' => 'photo'
        ];

        $data = [
            'photo' => $photoData,
            'og_data' => $ogData
        ];

        $this->view('photo_share/debug', $data);
    }
    
    /**
     * Generate photo title for sharing
     */
    private function generatePhotoTitle($photo) {
        // Start with event/show name for context
        $title = '';

        if ($photo['event_name'] && $photo['event_name'] !== 'Event Photo') {
            $title = $photo['event_name'];

            // Add caption if it exists and fits
            if ($photo['caption']) {
                $firstLine = strtok($photo['caption'], "\n\r");
                if (strlen($title . ' - ' . $firstLine) <= 80) {
                    $title .= ' - ' . $firstLine;
                } else {
                    $shortCaption = substr($firstLine, 0, 80 - strlen($title) - 6) . '...';
                    $title .= ' - ' . $shortCaption;
                }
            }
        } else {
            // Fallback if no event name
            $title = $photo['caption'] ? strtok($photo['caption'], "\n\r") : $photo['category_label'];
        }

        return $title;
    }
    
    /**
     * Generate photo description for sharing
     */
    private function generatePhotoDescription($photo) {
        $description = '';

        // Include caption if it exists
        if ($photo['caption']) {
            $description = $photo['caption'];
            // Add line break before event info
            $description .= "\n\n";
        }

        // Always include event context
        if ($photo['event_name'] && $photo['event_name'] !== 'Event Photo') {
            $description .= 'From: ' . $photo['event_name'];

            if ($photo['event_date']) {
                $description .= "\n📅 " . date('F j, Y', strtotime($photo['event_date']));
            }

            if ($photo['event_location']) {
                $description .= "\n📍 " . $photo['event_location'];
            }

            // Add link to view more photos
            $description .= "\n\n🔗 View more photos and event details";
        }

        // Limit total length for better Facebook display
        if (strlen($description) > 300) {
            $description = substr($description, 0, 297) . '...';
        }

        return $description;
    }
    
    /**
     * Check if user can view this photo based on privacy settings
     */
    private function canViewPhoto($photo) {
        // Public photos can be viewed by anyone
        if ($photo->privacy_level === 'public' || !$photo->privacy_level) {
            return true;
        }
        
        // Private photos can only be viewed by owner
        if ($photo->privacy_level === 'private') {
            return isset($_SESSION['user_id']) && $_SESSION['user_id'] == $photo->user_id;
        }
        
        // Event attendees only - for now, allow if logged in (can be enhanced later)
        if ($photo->privacy_level === 'event_only') {
            return isset($_SESSION['user_id']);
        }
        
        return false;
    }
    
    /**
     * Handle missing photo - redirect with appropriate message
     */
    private function handleMissingPhoto() {
        $data = [
            'title' => 'Photo Not Found | ' . APP_NAME,
            'pageTitle' => 'Photo Not Found',
            'pageDescription' => 'The requested photo is no longer available. Check out our latest event photos instead.',
            'pageImage' => BASE_URL . '/public/images/logo.png',
            'pageUrl' => BASE_URL . '/photo/not-found'
        ];
        
        $this->view('photo_share/not_found', $data);
    }
    
    /**
     * Handle private photo - show privacy message
     */
    private function handlePrivatePhoto() {
        $data = [
            'title' => 'Private Photo | ' . APP_NAME,
            'pageTitle' => 'Private Photo',
            'pageDescription' => 'This photo is private and cannot be viewed.',
            'pageImage' => BASE_URL . '/public/images/logo.png',
            'pageUrl' => BASE_URL . '/photo/private'
        ];
        
        $this->view('photo_share/private', $data);
    }
    
    /**
     * Get admin-configured category labels
     */
    private function getEventPhotoCategoryLabels() {
        try {
            $this->db->query('SELECT setting_name, setting_value FROM event_photo_settings 
                             WHERE setting_name LIKE "category_label_%"');
            $results = $this->db->resultSet();
            
            $labels = [
                'vehicle' => '🚗 Vehicle',
                'atmosphere' => '🎪 Atmosphere',
                'awards' => '🏆 Awards',
                'vendors' => '🍔 Vendors',
                'people' => '👥 People'
            ];
            
            // Override with admin settings if they exist
            foreach ($results as $setting) {
                $key = str_replace('category_label_', '', $setting->setting_name);
                if (isset($labels[$key])) {
                    $labels[$key] = $setting->setting_value;
                }
            }
            
            return $labels;
        } catch (Exception $e) {
            // Return defaults if there's an error
            return [
                'vehicle' => '🚗 Vehicle',
                'atmosphere' => '🎪 Atmosphere',
                'awards' => '🏆 Awards',
                'vendors' => '🍔 Vendors',
                'people' => '👥 People'
            ];
        }
    }
}
