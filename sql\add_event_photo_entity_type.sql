-- Add 'event_photo' to the entity_type enum in images table
-- This works with your existing image editor URL structure: /image_editor/upload/event_photo/5

ALTER TABLE images
MODIFY COLUMN entity_type ENUM('vehicle', 'show', 'user', 'event', 'other', 'event_photo') NOT NULL;

-- Event photos will use:
-- - entity_type = 'event_photo'
-- - entity_id = integer ID (5, 21, etc.) - same as your existing system
-- - event_photo_metadata table stores which type of event (show vs calendar event)
