<?php
/**
 * Photo Controller
 * 
 * Routes photo-related requests to appropriate controllers
 */

class PhotoController extends Controller {
    
    public function __construct() {
        // Initialize any required models
    }
    
    /**
     * Handle share requests - route to PhotoShareController
     *
     * @param int $photoId Photo ID
     */
    public function share($photoId = null) {
        // Load PhotoShareController and call its photo method
        require_once APPROOT . '/controllers/PhotoShareController.php';
        $photoShareController = new PhotoShareController();
        $photoShareController->photo($photoId);
    }

    /**
     * Handle debug requests - route to PhotoShareController debug method
     *
     * @param int $photoId Photo ID
     */
    public function debug($photoId = null) {
        // Load PhotoShareController and call its debug method
        require_once APPROOT . '/controllers/PhotoShareController.php';
        $photoShareController = new PhotoShareController();
        $photoShareController->debug($photoId);
    }
    
    /**
     * Default index method - redirect to home
     */
    public function index() {
        $this->redirect('home');
    }
}
