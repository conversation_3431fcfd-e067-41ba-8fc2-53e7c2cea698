<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Event Photo Moderation Settings
                    </h1>
                    <p class="text-muted">Configure content moderation, approval workflows, and reporting systems</p>
                </div>
                <div>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_event_photos" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Event Photos
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-gavel me-2"></i>
                        Moderation & Safety Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo BASE_URL; ?>/admin/event_photo_admin_moderation">
                        <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token'] ?? ''; ?>">
                        
                        <!-- Moderation Control -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-danger mb-3">
                                    <i class="fas fa-toggle-on me-2"></i>
                                    Moderation Control
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                    <input class="form-check-input" type="checkbox" id="moderation_enabled" name="moderation_enabled"
                                           style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                           <?php echo ($data['settings']['moderation_enabled'] ?? false) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="moderation_enabled">
                                        <i class="fas fa-shield-alt me-2 text-danger"></i>
                                        <strong>Enable Photo Moderation</strong>
                                        <small class="d-block text-muted">Require admin approval before photos are publicly visible</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                    <input class="form-check-input" type="checkbox" id="enable_reporting" name="enable_reporting"
                                           style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                           <?php echo ($data['settings']['enable_reporting'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="enable_reporting">
                                        <i class="fas fa-flag me-2 text-warning"></i>
                                        <strong>Enable Photo Reporting</strong>
                                        <small class="d-block text-muted">Allow users to report inappropriate photos</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Approval Workflows -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-danger mb-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    Approval Workflows
                                </h6>
                                <p class="text-muted">Configure automatic approval rules based on user trust levels</p>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                    <input class="form-check-input" type="checkbox" id="auto_approve_trusted_users" name="auto_approve_trusted_users"
                                           style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                           <?php echo ($data['settings']['auto_approve_trusted_users'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="auto_approve_trusted_users">
                                        <i class="fas fa-user-check me-2 text-success"></i>
                                        <strong>Auto-approve Trusted Users</strong>
                                        <small class="d-block text-muted">Automatically approve photos from trusted users</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                    <input class="form-check-input" type="checkbox" id="require_approval_new_users" name="require_approval_new_users"
                                           style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                           <?php echo ($data['settings']['require_approval_new_users'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="require_approval_new_users">
                                        <i class="fas fa-user-plus me-2 text-info"></i>
                                        <strong>Require Approval for New Users</strong>
                                        <small class="d-block text-muted">New users must have photos approved manually</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="trusted_user_threshold_photos" class="form-label">Trusted User Threshold</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="trusted_user_threshold_photos" name="trusted_user_threshold_photos" 
                                           value="<?php echo $data['settings']['trusted_user_threshold_photos'] ?? 10; ?>" 
                                           min="1" max="100" required>
                                    <span class="input-group-text">photos</span>
                                </div>
                                <small class="text-muted">Number of approved photos needed to become trusted</small>
                            </div>
                        </div>

                        <!-- Automated Detection -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-danger mb-3">
                                    <i class="fas fa-robot me-2"></i>
                                    Automated Detection
                                </h6>
                                <p class="text-muted">Automatic flagging of potentially inappropriate content</p>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                    <input class="form-check-input" type="checkbox" id="auto_flag_inappropriate" name="auto_flag_inappropriate"
                                           style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                           <?php echo ($data['settings']['auto_flag_inappropriate'] ?? false) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="auto_flag_inappropriate">
                                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                                        <strong>Auto-flag Inappropriate Content</strong>
                                        <small class="d-block text-muted">Automatically flag photos that may contain inappropriate content</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Notification Settings -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-danger mb-3">
                                    <i class="fas fa-bell me-2"></i>
                                    Notification Settings
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                    <input class="form-check-input" type="checkbox" id="moderation_email_notifications" name="moderation_email_notifications"
                                           style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                           <?php echo ($data['settings']['moderation_email_notifications'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="moderation_email_notifications">
                                        <i class="fas fa-envelope me-2 text-primary"></i>
                                        <strong>Email Notifications for Moderation</strong>
                                        <small class="d-block text-muted">Send email alerts when photos need moderation</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Moderation Guidelines -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-danger">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        Moderation Guidelines
                                    </h6>
                                    <ul class="mb-0">
                                        <li><strong>Content Standards:</strong> Photos should be appropriate for all audiences</li>
                                        <li><strong>Privacy Respect:</strong> No photos of people without their consent</li>
                                        <li><strong>Event Relevance:</strong> Photos should be related to the event</li>
                                        <li><strong>Quality Standards:</strong> Clear, well-lit photos are preferred</li>
                                        <li><strong>No Spam:</strong> Avoid duplicate or low-quality submissions</li>
                                        <li><strong>Copyright:</strong> Only upload photos you own or have permission to share</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Moderation Workflow -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>
                                        How Moderation Works
                                    </h6>
                                    <ol class="mb-0">
                                        <li><strong>Photo Upload:</strong> User uploads photo to event gallery</li>
                                        <li><strong>Initial Check:</strong> System checks user trust level and auto-flags</li>
                                        <li><strong>Approval Queue:</strong> Photos requiring approval go to moderation queue</li>
                                        <li><strong>Admin Review:</strong> Admins approve, reject, or request changes</li>
                                        <li><strong>Publication:</strong> Approved photos become visible to other users</li>
                                        <li><strong>Ongoing Monitoring:</strong> Users can report inappropriate content</li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <button type="submit" class="btn btn-danger btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    Save Moderation Settings
                                </button>
                                <a href="<?php echo BASE_URL; ?>/admin/settings_event_photos" class="btn btn-outline-secondary btn-lg ms-2">
                                    <i class="fas fa-times me-2"></i>
                                    Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-check-input:checked {
    background-color: #dc3545;
    border-color: #dc3545;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.text-danger {
    color: #dc3545 !important;
}

.input-group-text {
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.form-control:focus, .form-select:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
