/**
 * Sprint 1 UX Components CSS
 * Styles for role indicators, guest conversion, progressive registration, breadcrumbs, and mobile enhancements
 */

/* ==========================================================================
   Role Indicator Component
   ========================================================================== */

.role-indicator-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 15px;
}

.role-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    color: white;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    cursor: help;
    position: relative;
    overflow: hidden;
}

.role-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.role-badge i {
    font-size: 1rem;
}

.role-text {
    font-family: 'Orbitron', monospace;
}

/* Role upgrade celebration animation */
.role-badge.role-upgraded {
    animation: roleUpgrade 2s ease-in-out;
}

.upgrade-celebration {
    position: absolute;
    top: -10px;
    right: -10px;
    display: flex;
    gap: 2px;
}

.upgrade-celebration i {
    color: #FFD700;
    font-size: 0.7rem;
    animation: starTwinkle 1.5s ease-in-out infinite;
}

.upgrade-celebration i:nth-child(2) {
    animation-delay: 0.2s;
}

.upgrade-celebration i:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes roleUpgrade {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); box-shadow: 0 0 20px rgba(255, 215, 0, 0.6); }
    100% { transform: scale(1); }
}

@keyframes starTwinkle {
    0%, 100% { opacity: 0.3; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
}

/* Role switcher styling */
.role-switcher .btn {
    border-color: rgba(255,255,255,0.3);
    color: rgba(255,255,255,0.8);
}

.role-switcher .btn:hover {
    background-color: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

/* ==========================================================================
   Guest Conversion Overlay
   ========================================================================== */

.guest-conversion-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: overlayFadeIn 0.3s ease-out;
}

.overlay-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.conversion-modal {
    position: relative;
    background: white;
    border-radius: 16px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.4s ease-out;
}

.close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f5f5f5;
    color: #333;
}

.conversion-header {
    text-align: center;
    margin-bottom: 25px;
}

.conversion-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
}

.conversion-header h3 {
    color: #333;
    margin-bottom: 10px;
    font-weight: 700;
}

.conversion-subtitle {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
}

.social-proof {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 25px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

.proof-item {
    text-align: center;
}

.proof-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: #dc3545;
}

.proof-label {
    font-size: 0.9rem;
    color: #666;
}

/* ==========================================================================
   Breadcrumb Navigation
   ========================================================================== */

.breadcrumb-navigation {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 12px 0;
    margin-bottom: 20px;
}

.breadcrumb-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;
}

.breadcrumb-trail {
    flex: 1;
}

.breadcrumb {
    margin: 0;
    padding: 0;
    background: none;
    font-size: 0.9rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    margin: 0 8px;
    font-weight: bold;
}

.breadcrumb-item a {
    color: #495057;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.breadcrumb-item a:hover {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.breadcrumb-item.active span {
    color: #dc3545;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.breadcrumb-item i {
    font-size: 0.85rem;
}

.breadcrumb-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.role-context {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
}

.role-label {
    color: #6c757d;
    font-weight: 500;
}

.role-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
}

.role-badge.role-user { background: #4a148c; }
.role-badge.role-staff { background: #1b5e20; }
.role-badge.role-judge { background: #e65100; }
.role-badge.role-coordinator { background: #880e4f; }
.role-badge.role-admin { background: #b71c1c; }

/* ==========================================================================
   Dashboard Quick Actions
   ========================================================================== */

.dashboard-quick-actions {
    background: white !important;
    border-radius: 12px !important;
    padding: 25px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 30px !important;
}

.dashboard-quick-actions .quick-actions-header {
    text-align: center !important;
    margin-bottom: 25px !important;
}

.dashboard-quick-actions .quick-actions-header h5 {
    color: #333 !important;
    margin-bottom: 8px !important;
    font-weight: 700 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    font-size: 1.25rem !important;
}

.dashboard-quick-actions .quick-actions-header h5 i {
    color: #dc3545 !important;
    font-size: 1.3rem !important;
}

.dashboard-quick-actions .quick-actions-header p {
    margin: 0 !important;
    font-size: 0.9rem !important;
    color: #666 !important;
}

.dashboard-quick-actions .quick-actions-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 15px !important;
}

.dashboard-quick-actions .quick-action-card {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    padding: 20px !important;
    border: 2px solid #f8f9fa !important;
    border-radius: 10px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    background: #fafbfc !important;
    text-decoration: none !important;
}

.dashboard-quick-actions .quick-action-card:hover {
    border-color: #dc3545 !important;
    background: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.1) !important;
    text-decoration: none !important;
}

.dashboard-quick-actions .action-icon {
    width: 50px !important;
    height: 50px !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 1.3rem !important;
    flex-shrink: 0 !important;
}

.dashboard-quick-actions .action-content {
    flex: 1 !important;
}

.dashboard-quick-actions .action-title {
    color: #333 !important;
    margin-bottom: 5px !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    flex-wrap: wrap !important;
}

.dashboard-quick-actions .action-description {
    color: #666 !important;
    margin: 0 !important;
    font-size: 0.85rem !important;
    line-height: 1.4 !important;
}

.dashboard-quick-actions .action-arrow {
    color: #ccc !important;
    font-size: 1.1rem !important;
    transition: all 0.3s ease !important;
}

.dashboard-quick-actions .quick-action-card:hover .action-arrow {
    color: #dc3545 !important;
    transform: translateX(3px) !important;
}

/* ==========================================================================
   Mobile Responsive Styles
   ========================================================================== */

@media (max-width: 768px) {
    .role-indicator-container {
        margin-left: 8px;
    }
    
    .role-badge {
        padding: 4px 8px;
        font-size: 0.75rem;
    }
    
    .role-text {
        display: none; /* Show only icon on mobile */
    }
    
    .role-switcher {
        display: none; /* Hide switcher on mobile */
    }
    
    .conversion-modal {
        padding: 20px;
        margin: 20px;
        width: calc(100% - 40px);
    }
    
    .social-proof {
        gap: 20px;
    }
    
    .proof-number {
        font-size: 1.5rem;
    }
    
    .breadcrumb-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .breadcrumb {
        font-size: 0.8rem;
    }
    
    .breadcrumb-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .dashboard-quick-actions {
        padding: 20px 15px;
        margin-bottom: 20px;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .quick-action-card {
        padding: 15px;
        gap: 12px;
    }
}

/* Hide breadcrumbs on very small screens */
@media (max-width: 480px) {
    .breadcrumb-navigation {
        display: none;
    }
}

/* ==========================================================================
   Animation Keyframes
   ========================================================================== */

@keyframes overlayFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from { 
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.text-purple { color: #4a148c !important; }
.text-pink { color: #880e4f !important; }
.text-orange { color: #e65100 !important; }
.text-green { color: #1b5e20 !important; }
.text-red { color: #b71c1c !important; }

/* ==========================================================================
   Quick Actions Color Variations & Badge Styling
   ========================================================================== */

/* Color variations for quick actions */
.dashboard-quick-actions .action-icon.bg-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}
.dashboard-quick-actions .action-icon.bg-success {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
}
.dashboard-quick-actions .action-icon.bg-info {
    background: linear-gradient(135deg, #17a2b8, #117a8b) !important;
}
.dashboard-quick-actions .action-icon.bg-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800) !important;
}
.dashboard-quick-actions .action-icon.bg-danger {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
}

/* Action badge styling */
.dashboard-quick-actions .action-badge {
    font-size: 0.7rem !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-weight: 500 !important;
    margin-left: 5px !important;
    background-color: #dc3545 !important;
    color: white !important;
}

/* Pulse animation for "Start Here!" badge */
.dashboard-quick-actions .quick-action-card:has(.action-badge) {
    animation: pulseGlow 2s ease-in-out infinite !important;
    border-color: #dc3545 !important;
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.1) !important;
    }
    50% {
        box-shadow: 0 4px 20px rgba(220, 53, 69, 0.3) !important;
    }
}

/* ==========================================================================
   Enhanced Mobile Navigation with "More" Menu
   ========================================================================== */

/* Mobile "More" menu overlay */
.mobile-more-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.more-menu-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
}

.more-menu-content {
    position: relative;
    background: white;
    border-radius: 20px 20px 0 0;
    width: 100%;
    max-height: 70vh;
    overflow-y: auto;
    animation: slideUpMenu 0.3s ease-out;
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.2);
}

.more-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 20px 10px;
    border-bottom: 1px solid #eee;
}

.more-menu-header h6 {
    margin: 0;
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.more-menu-header .btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.more-menu-header .btn-close:hover {
    background: #f5f5f5;
    color: #333;
}

.more-menu-header .btn-close::before {
    content: '×';
}

.more-menu-items {
    padding: 10px 0 20px;
}

.more-menu-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f8f9fa;
}

.more-menu-item:hover {
    background: #f8f9fa;
    color: #dc3545;
    text-decoration: none;
}

.more-menu-item:last-child {
    border-bottom: none;
}

.more-menu-item i {
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    transition: color 0.2s ease;
}

.more-menu-item:hover i {
    color: #dc3545;
}

.more-menu-item span {
    font-weight: 500;
    font-size: 1rem;
}

/* Enhanced mobile nav item styling */
.mobile-nav-item.active {
    color: #dc3545 !important;
}

.mobile-nav-item.active i {
    color: #dc3545 !important;
}

#mobile-more-btn.active {
    color: #dc3545 !important;
}

#mobile-more-btn.active i {
    color: #dc3545 !important;
}

/* Slide up animation for more menu */
@keyframes slideUpMenu {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Mobile responsive adjustments */
@media (max-width: 480px) {
    .more-menu-content {
        border-radius: 15px 15px 0 0;
    }

    .more-menu-header {
        padding: 15px 15px 8px;
    }

    .more-menu-item {
        padding: 12px 15px;
        gap: 12px;
    }

    .more-menu-item i {
        font-size: 1rem;
    }

    .more-menu-item span {
        font-size: 0.9rem;
    }
}

/* ==========================================================================
   Enhanced Racing Drawer Organization
   ========================================================================== */

/* Add visual grouping to racing drawer */
.racing-drawer-content {
    position: relative;
}

/* Add section headers for better organization */
.racing-drawer .racing-section-header {
    padding: 15px 20px 10px !important;
    margin: 10px 0 5px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    background: rgba(0, 0, 0, 0.2) !important;
    display: block !important;
}

.racing-drawer .racing-section-header:first-of-type {
    margin-top: 0 !important;
}

/* Improve racing button spacing and grouping */
.racing-drawer .racing-buttons-grid {
    padding: 10px !important;
}

.racing-drawer .racing-button-group {
    margin-bottom: 15px !important;
    padding-bottom: 15px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.racing-drawer .racing-button-group:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

/* Improve racing button touch targets for mobile */
.racing-drawer .racing-button {
    margin-bottom: 8px !important;
    min-height: 50px !important;
    display: flex !important;
    align-items: center !important;
}

.racing-drawer .racing-button .button-chrome-bezel {
    width: 100% !important;
    min-height: 50px !important;
    display: flex !important;
    align-items: center !important;
}

.racing-drawer .racing-button .button-content {
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    padding: 12px 15px !important;
}

.racing-drawer .racing-button .button-content i {
    font-size: 1.1rem !important;
    width: 20px !important;
    text-align: center !important;
}

.racing-drawer .racing-button .button-content span {
    font-size: 0.9rem !important;
    font-weight: 600 !important;
}

/* Special styling for logout button */
.racing-drawer .racing-button.logout-button {
    margin-top: 20px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding-top: 15px !important;
}

/* Improve submenu organization */
.racing-drawer .racing-submenu {
    background: rgba(0, 0, 0, 0.3) !important;
    border-radius: 8px !important;
    margin: 5px 0 15px !important;
    overflow: hidden !important;
}

.racing-drawer .racing-submenu .submenu-item {
    padding: 12px 20px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
    transition: all 0.2s ease !important;
}

.racing-drawer .racing-submenu .submenu-item:last-child {
    border-bottom: none !important;
}

.racing-drawer .racing-submenu .submenu-item:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    padding-left: 25px !important;
}

.racing-drawer .racing-submenu .submenu-item i {
    width: 18px !important;
    margin-right: 10px !important;
    font-size: 0.9rem !important;
    opacity: 0.8 !important;
}

/* Add subtle animations */
.racing-drawer .racing-button {
    transition: all 0.2s ease !important;
}

.racing-drawer .racing-button:hover {
    transform: translateX(3px) !important;
}

.racing-drawer .racing-button:active {
    transform: translateX(1px) scale(0.98) !important;
}

/* Improve drawer header */
.racing-drawer .racing-drawer-header {
    padding: 20px !important;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1)) !important;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
    position: relative !important;
}

.racing-drawer .racing-title {
    font-size: 1.2rem !important;
    margin: 10px 0 !important;
    text-align: center !important;
    color: #fff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

/* Add close button to drawer header */
.racing-drawer .racing-drawer-close {
    position: absolute !important;
    top: 15px !important;
    right: 15px !important;
    background: none !important;
    border: none !important;
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 1.5rem !important;
    cursor: pointer !important;
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s ease !important;
    z-index: 10 !important;
}

.racing-drawer .racing-drawer-close:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #fff !important;
}

/* Responsive improvements */
@media (max-width: 480px) {
    .racing-drawer-content {
        padding: 0;
    }

    .racing-buttons-grid {
        padding: 5px;
    }

    .racing-button .button-content {
        padding: 10px 12px;
        gap: 10px;
    }

    .racing-button .button-content span {
        font-size: 0.85rem;
    }

    .racing-section-header {
        padding: 12px 15px 8px;
        font-size: 0.8rem;
    }
}

/* ==========================================================================
   Bootstrap Override Fixes for Better Visibility
   ========================================================================== */

/* Force visibility of key elements */
.dashboard-quick-actions .quick-actions-header h5,
.dashboard-quick-actions .quick-actions-header p,
.dashboard-quick-actions .action-title,
.dashboard-quick-actions .action-description {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure icons are visible */
.dashboard-quick-actions .fas,
.role-indicator .fas,
.breadcrumb-nav .fas {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fix text color inheritance issues */
.dashboard-quick-actions * {
    color: inherit !important;
}

.dashboard-quick-actions .quick-actions-header h5 {
    color: #333 !important;
}

.dashboard-quick-actions .quick-actions-header p {
    color: #666 !important;
}

/* Fix margin/padding resets */
.dashboard-quick-actions .quick-actions-header h5 {
    margin-bottom: 8px !important;
    margin-top: 0 !important;
}

.dashboard-quick-actions .quick-actions-header p {
    margin-bottom: 0 !important;
    margin-top: 0 !important;
}

/* Ensure proper spacing */
.dashboard-quick-actions .quick-actions-header {
    margin-bottom: 25px !important;
    padding: 0 !important;
}

/* Fix flexbox issues */
.dashboard-quick-actions .quick-actions-header h5 {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
}

/* ==========================================================================
   Enhanced Context-Aware FAB
   ========================================================================== */

/* Primary action styling for FAB */
.fab-action.primary-action {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: white !important;
    font-weight: 600 !important;
    min-width: 120px !important;
    justify-content: flex-start !important;
    padding: 12px 16px !important;
    border-radius: 25px !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
}

.fab-action.primary-action:hover {
    background: linear-gradient(135deg, #20c997, #28a745) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
}

.fab-action.primary-action i {
    margin-right: 8px !important;
    font-size: 1rem !important;
}

.fab-action.primary-action span {
    font-size: 0.9rem !important;
    font-weight: 600 !important;
}

/* Context-specific FAB main button colors */
.fab-main.show-context {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.fab-main.vehicle-context {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.fab-main.dashboard-context {
    background: linear-gradient(135deg, #6f42c1, #5a32a3) !important;
}

.fab-main.admin-context {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
}

.fab-main.calendar-context {
    background: linear-gradient(135deg, #fd7e14, #e55a00) !important;
}

/* Enhanced FAB actions layout */
.fab-actions.show {
    display: flex !important;
    flex-direction: column-reverse !important;
    gap: 10px !important;
    margin-bottom: 15px !important;
}

.fab-action {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.95) !important;
    color: #333 !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15) !important;
    backdrop-filter: blur(10px) !important;
}

.fab-action:hover {
    background: white !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* FAB tooltip enhancement */
.fab-action[title]:hover::after {
    content: attr(title);
    position: absolute;
    right: 60px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

/* Mobile responsive FAB */
@media (max-width: 480px) {
    .fab-action.primary-action {
        min-width: 100px !important;
        padding: 10px 14px !important;
    }

    .fab-action.primary-action span {
        font-size: 0.8rem !important;
    }
}
