<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-6 text-center">
            <div class="card shadow-sm">
                <div class="card-body p-5">
                    <div class="mb-4">
                        <i class="fas fa-lock text-warning" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h2 class="mb-3">Private Photo</h2>
                    <p class="text-muted mb-4">
                        This photo is private and cannot be viewed publicly. Only the photographer can access this content.
                    </p>
                    
                    <div class="d-flex flex-column gap-3">
                        <?php if (!isset($_SESSION['user_id'])): ?>
                            <a href="<?php echo BASE_URL; ?>/auth/login" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>Login to View
                            </a>
                        <?php endif; ?>
                        
                        <a href="<?php echo BASE_URL; ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>Back to <?php echo APP_NAME; ?>
                        </a>
                        
                        <a href="<?php echo BASE_URL; ?>/image_editor/browse?type=event_photo" class="btn btn-outline-info">
                            <i class="fas fa-images me-2"></i>Browse Public Photos
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Site Promotion -->
            <div class="card mt-4 bg-light">
                <div class="card-body text-center">
                    <h6 class="mb-2">
                        <i class="fas fa-star text-warning me-2"></i>
                        Join Our Community
                    </h6>
                    <p class="text-muted mb-3">
                        Create an account to share your own photos and connect with fellow car enthusiasts!
                    </p>
                    <a href="<?php echo BASE_URL; ?>/auth/register" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>Join <?php echo APP_NAME; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
