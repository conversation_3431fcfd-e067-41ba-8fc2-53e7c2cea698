<!-- This file has been replaced by the updated shows.php -->

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?php echo $_SESSION['flash_message']['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['flash_message']['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-6 col-md-2 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo $stats->total_shows; ?></h4>
                            <p class="mb-0 small">Total Shows</p>
                        </div>
                        <div class="align-self-center d-none d-md-block">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6 col-md-2 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo $stats->active_shows; ?></h4>
                            <p class="mb-0 small">Active Shows</p>
                        </div>
                        <div class="align-self-center d-none d-md-block">
                            <i class="fas fa-play-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6 col-md-2 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo $stats->upcoming_shows; ?></h4>
                            <p class="mb-0 small">Upcoming</p>
                        </div>
                        <div class="align-self-center d-none d-md-block">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6 col-md-2 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo $stats->total_registrations; ?></h4>
                            <p class="mb-0 small">Registrations</p>
                        </div>
                        <div class="align-self-center d-none d-md-block">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6 col-md-2 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">$<?php echo number_format($stats->total_revenue, 2); ?></h4>
                            <p class="mb-0 small">Total Revenue</p>
                        </div>
                        <div class="align-self-center d-none d-md-block">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6 col-md-2 mb-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo $stats->pending_payments; ?></h4>
                            <p class="mb-0 small">Pending Payments</p>
                        </div>
                        <div class="align-self-center d-none d-md-block">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 col-md-3 col-lg-2 mb-2">
                            <?php if ($is_admin): ?>
                                <a href="<?php echo BASE_URL; ?>/admin/addShow" class="btn btn-primary btn-sm w-100">
                                    <i class="fas fa-plus me-2"></i>Create New Show
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="col-6 col-md-3 col-lg-2 mb-2">
                            <a href="<?php echo BASE_URL; ?>/admin/registrations" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-users me-2"></i>All Registrations
                            </a>
                        </div>
                        <div class="col-6 col-md-3 col-lg-2 mb-2">
                            <a href="<?php echo BASE_URL; ?>/admin/manageScores" class="btn btn-outline-success btn-sm w-100">
                                <i class="fas fa-trophy me-2"></i>Manage Scoring
                            </a>
                        </div>
                        <?php if ($is_admin): ?>
                            <div class="col-6 col-md-3 col-lg-2 mb-2">
                                <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-outline-secondary btn-sm w-100">
                                    <i class="fas fa-cog me-2"></i>System Settings
                                </a>
                            </div>
                            <div class="col-6 col-md-3 col-lg-2 mb-2">
                                <a href="<?php echo BASE_URL; ?>/admin/fixPendingShowPayments" class="btn btn-outline-warning btn-sm w-100">
                                    <i class="fas fa-wrench me-2"></i>Fix Pending Payments
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Shows Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                        <h5 class="card-title mb-2 mb-md-0">
                            <i class="fas fa-list me-2"></i>Shows Management
                        </h5>
                        <div class="d-flex flex-column flex-md-row gap-2 w-100 w-md-auto">
                            <!-- Search -->
                            <div class="input-group input-group-sm" style="min-width: 250px;">
                                <input type="text" id="showSearch" class="form-control" 
                                       placeholder="Search shows..." value="<?php echo htmlspecialchars($current_search); ?>">
                                <button class="btn btn-outline-light" type="button" id="searchButton">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters and Controls -->
                    <div class="row mb-3">
                        <div class="col-12 col-lg-8 mb-3 mb-lg-0">
                            <div class="btn-group flex-wrap" role="group" aria-label="Filter shows">
                                <a href="<?php echo BASE_URL; ?>/admin/shows?status=all&sort=<?php echo $current_sort; ?>&order=<?php echo $current_order; ?>&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>" 
                                   class="btn btn-outline-primary btn-sm <?php echo $current_status === 'all' ? 'active' : ''; ?>">All</a>
                                <a href="<?php echo BASE_URL; ?>/admin/shows?status=upcoming&sort=<?php echo $current_sort; ?>&order=<?php echo $current_order; ?>&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>" 
                                   class="btn btn-outline-info btn-sm <?php echo $current_status === 'upcoming' ? 'active' : ''; ?>">Upcoming</a>
                                <a href="<?php echo BASE_URL; ?>/admin/shows?status=active&sort=<?php echo $current_sort; ?>&order=<?php echo $current_order; ?>&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>" 
                                   class="btn btn-outline-success btn-sm <?php echo $current_status === 'active' ? 'active' : ''; ?>">Active</a>
                                <a href="<?php echo BASE_URL; ?>/admin/shows?status=completed&sort=<?php echo $current_sort; ?>&order=<?php echo $current_order; ?>&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>" 
                                   class="btn btn-outline-secondary btn-sm <?php echo $current_status === 'completed' ? 'active' : ''; ?>">Completed</a>
                                <a href="<?php echo BASE_URL; ?>/admin/shows?status=cancelled&sort=<?php echo $current_sort; ?>&order=<?php echo $current_order; ?>&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>" 
                                   class="btn btn-outline-danger btn-sm <?php echo $current_status === 'cancelled' ? 'active' : ''; ?>">Cancelled</a>
                            </div>
                        </div>
                        <div class="col-12 col-lg-4">
                            <div class="d-flex flex-column flex-sm-row justify-content-end gap-2">
                                <!-- Per Page Selector -->
                                <div class="d-flex align-items-center">
                                    <label for="perPageSelect" class="form-label me-2 mb-0 small">Show:</label>
                                    <select id="perPageSelect" class="form-select form-select-sm" style="width: auto;">
                                        <option value="10" <?php echo $pagination['per_page'] == 10 ? 'selected' : ''; ?>>10</option>
                                        <option value="25" <?php echo $pagination['per_page'] == 25 ? 'selected' : ''; ?>>25</option>
                                        <option value="50" <?php echo $pagination['per_page'] == 50 ? 'selected' : ''; ?>>50</option>
                                        <option value="100" <?php echo $pagination['per_page'] == 100 ? 'selected' : ''; ?>>100</option>
                                    </select>
                                </div>
                                
                                <!-- Bulk Actions -->
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" 
                                            id="bulkActionsDropdown" data-bs-toggle="dropdown" aria-expanded="false" disabled>
                                        Bulk Actions
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="bulkActionsDropdown">
                                        <li><a class="dropdown-item bulk-action" href="#" data-action="activate">
                                            <i class="fas fa-play me-2"></i>Activate Selected</a></li>
                                        <li><a class="dropdown-item bulk-action" href="#" data-action="complete">
                                            <i class="fas fa-check me-2"></i>Mark as Completed</a></li>
                                        <li><a class="dropdown-item bulk-action" href="#" data-action="cancel">
                                            <i class="fas fa-times me-2"></i>Cancel Selected</a></li>
                                        <?php if ($is_admin): ?>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item bulk-action text-danger" href="#" data-action="delete">
                                                <i class="fas fa-trash me-2"></i>Delete Selected</a></li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                                
                                <!-- Sort Options -->
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" 
                                            id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        Sort: <?php echo ucfirst(str_replace('_', ' ', $current_sort)); ?>
                                        <i class="fas fa-sort-<?php echo $current_order === 'asc' ? 'up' : 'down'; ?> ms-1"></i>
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/shows?status=<?php echo $current_status; ?>&sort=name&order=asc&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>">Name (A-Z)</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/shows?status=<?php echo $current_status; ?>&sort=name&order=desc&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>">Name (Z-A)</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/shows?status=<?php echo $current_status; ?>&sort=start_date&order=desc&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>">Date (Newest)</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/shows?status=<?php echo $current_status; ?>&sort=start_date&order=asc&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>">Date (Oldest)</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/shows?status=<?php echo $current_status; ?>&sort=registration_count&order=desc&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>">Most Registrations</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/shows?status=<?php echo $current_status; ?>&sort=created_at&order=desc&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>">Recently Created</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination Info -->
                    <?php if ($pagination['total_records'] > 0): ?>
                        <div class="row mb-3">
                            <div class="col-12">
                                <small class="text-muted">
                                    Showing <?php echo $pagination['start_record']; ?> to <?php echo $pagination['end_record']; ?> 
                                    of <?php echo $pagination['total_records']; ?> shows
                                </small>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Shows Table -->
                    <?php if (empty($shows)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No shows found matching your criteria.
                        </div>
                    <?php else: ?>
                        <form id="bulkActionForm" method="POST" action="<?php echo BASE_URL; ?>/admin/quickAction">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                            <input type="hidden" name="action" id="bulkActionInput">
                            
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="showsTable">
                                    <thead>
                                        <tr>
                                            <th width="30">
                                                <input type="checkbox" id="selectAll" class="form-check-input">
                                            </th>
                                            <th>Show Details</th>
                                            <th class="d-none d-md-table-cell">Dates</th>
                                            <th>Status</th>
                                            <th class="d-none d-lg-table-cell">Registrations</th>
                                            <th class="d-none d-lg-table-cell">Revenue</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($shows as $show): ?>
                                            <tr class="show-row">
                                                <td>
                                                    <input type="checkbox" name="show_ids[]" value="<?php echo $show->id; ?>" 
                                                           class="form-check-input show-checkbox">
                                                </td>
                                                <td>
                                                    <div>
                                                        <h6 class="mb-1">
                                                            <a href="<?php echo BASE_URL; ?>/show/view/<?php echo $show->id; ?>"
                                                               class="text-decoration-none">
                                                                <?php echo htmlspecialchars($show->name); ?>
                                                            </a>
                                                        </h6>
                                                        <small class="text-muted">
                                                            <i class="fas fa-map-marker-alt me-1"></i>
                                                            <?php echo htmlspecialchars($show->location); ?>
                                                        </small>
                                                        <?php if (!empty($show->coordinator_name)): ?>
                                                            <br><small class="text-muted">
                                                                <i class="fas fa-user me-1"></i>
                                                                <?php echo htmlspecialchars($show->coordinator_name); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                        <!-- Mobile-only info -->
                                                        <div class="d-md-none mt-2">
                                                            <div class="small">
                                                                <?php if (!empty($show->start_date)): ?>
                                                                    <div><strong>Start:</strong> <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></div>
                                                                <?php endif; ?>
                                                                <div><strong><?php echo $show->registration_count; ?></strong> registrations</div>
                                                                <div><strong>$<?php echo number_format($show->total_revenue, 2); ?></strong> revenue</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="d-none d-md-table-cell">
                                                    <div class="small">
                                                        <?php if (!empty($show->start_date)): ?>
                                                            <div><strong>Start:</strong> <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></div>
                                                        <?php endif; ?>
                                                        <?php if (!empty($show->end_date) && $show->end_date !== $show->start_date): ?>
                                                            <div><strong>End:</strong> <?php echo formatDateTimeForUser($show->end_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></div>
                                                        <?php endif; ?>
                                                        <?php if (!empty($show->registration_end)): ?>
                                                            <div class="text-warning"><strong>Reg End:</strong> <?php echo formatDateTimeForUser($show->registration_end, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></div>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusClass = 'secondary';
                                                    switch ($show->status) {
                                                        case 'upcoming': $statusClass = 'primary'; break;
                                                        case 'active': $statusClass = 'success'; break;
                                                        case 'completed': $statusClass = 'secondary'; break;
                                                        case 'cancelled': $statusClass = 'danger'; break;
                                                        case 'draft': $statusClass = 'warning'; break;
                                                        case 'payment_pending': $statusClass = 'info'; break;
                                                    }
                                                    ?>
                                                    <span class="badge bg-<?php echo $statusClass; ?>">
                                                        <?php echo ucfirst(str_replace('_', ' ', $show->status)); ?>
                                                    </span>
                                                </td>
                                                <td class="d-none d-lg-table-cell">
                                                    <div class="small">
                                                        <div><strong><?php echo $show->registration_count; ?></strong> total</div>
                                                        <div class="text-success"><?php echo $show->paid_registrations; ?> paid</div>
                                                        <?php if ($show->pending_registrations > 0): ?>
                                                            <div class="text-warning"><?php echo $show->pending_registrations; ?> pending</div>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td class="d-none d-lg-table-cell">
                                                    <strong>$<?php echo number_format($show->total_revenue, 2); ?></strong>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="<?php echo BASE_URL; ?>/admin/registrations/<?php echo $show->id; ?>" 
                                                           class="btn btn-sm btn-outline-primary" title="View Registrations">
                                                            <i class="fas fa-users"></i>
                                                        </a>
                                                        <a href="<?php echo BASE_URL; ?>/admin/editShow/<?php echo $show->id; ?>" 
                                                           class="btn btn-sm btn-outline-secondary" title="Edit Show">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <div class="btn-group" role="group">
                                                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                                    data-bs-toggle="dropdown" aria-expanded="false" title="More Actions">
                                                                <i class="fas fa-ellipsis-v"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/show/view/<?php echo $show->id; ?>">
                                                                    <i class="fas fa-eye me-2"></i>View Public Page</a></li>
                                                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/judgingMetrics/<?php echo $show->id; ?>">
                                                                    <i class="fas fa-balance-scale me-2"></i>Judging Metrics</a></li>
                                                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/manageScores?show_id=<?php echo $show->id; ?>">
                                                                    <i class="fas fa-trophy me-2"></i>Manage Scores</a></li>
                                                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/exportRegistrations/<?php echo $show->id; ?>">
                                                                    <i class="fas fa-file-excel me-2"></i>Export Registrations</a></li>
                                                                <?php if ($show->fan_voting_enabled): ?>
                                                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/fanVotes/<?php echo $show->id; ?>">
                                                                        <i class="fas fa-heart me-2"></i>Fan Votes</a></li>
                                                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/qrCodes/<?php echo $show->id; ?>">
                                                                        <i class="fas fa-qrcode me-2"></i>QR Codes</a></li>
                                                                <?php endif; ?>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/duplicateShow/<?php echo $show->id; ?>">
                                                                    <i class="fas fa-copy me-2"></i>Duplicate Show</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </form>
                    <?php endif; ?>

                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                        <div class="row mt-3">
                            <div class="col-12">
                                <nav aria-label="Shows pagination">
                                    <ul class="pagination pagination-sm justify-content-center">
                                        <?php if ($pagination['has_prev']): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?php echo BASE_URL; ?>/admin/shows?status=<?php echo $current_status; ?>&sort=<?php echo $current_sort; ?>&order=<?php echo $current_order; ?>&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>&page=<?php echo $pagination['prev_page']; ?>">
                                                    <i class="fas fa-chevron-left"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <?php
                                        $start_page = max(1, $pagination['current_page'] - 2);
                                        $end_page = min($pagination['total_pages'], $pagination['current_page'] + 2);
                                        
                                        if ($start_page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?php echo BASE_URL; ?>/admin/shows?status=<?php echo $current_status; ?>&sort=<?php echo $current_sort; ?>&order=<?php echo $current_order; ?>&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>&page=1">1</a>
                                            </li>
                                            <?php if ($start_page > 2): ?>
                                                <li class="page-item disabled"><span class="page-link">...</span></li>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        
                                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                            <li class="page-item <?php echo $i == $pagination['current_page'] ? 'active' : ''; ?>">
                                                <a class="page-link" href="<?php echo BASE_URL; ?>/admin/shows?status=<?php echo $current_status; ?>&sort=<?php echo $current_sort; ?>&order=<?php echo $current_order; ?>&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>
                                        
                                        <?php if ($end_page < $pagination['total_pages']): ?>
                                            <?php if ($end_page < $pagination['total_pages'] - 1): ?>
                                                <li class="page-item disabled"><span class="page-link">...</span></li>
                                            <?php endif; ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?php echo BASE_URL; ?>/admin/shows?status=<?php echo $current_status; ?>&sort=<?php echo $current_sort; ?>&order=<?php echo $current_order; ?>&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>&page=<?php echo $pagination['total_pages']; ?>"><?php echo $pagination['total_pages']; ?></a>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <?php if ($pagination['has_next']): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?php echo BASE_URL; ?>/admin/shows?status=<?php echo $current_status; ?>&sort=<?php echo $current_sort; ?>&order=<?php echo $current_order; ?>&search=<?php echo urlencode($current_search); ?>&per_page=<?php echo $pagination['per_page']; ?>&page=<?php echo $pagination['next_page']; ?>">
                                                    <i class="fas fa-chevron-right"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities and Upcoming Deadlines -->
    <div class="row">
        <!-- Recent Activities -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Activities
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_activities)): ?>
                        <p class="text-muted mb-0">No recent activities</p>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="list-group-item px-0 py-2">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="small">
                                            <strong><?php echo htmlspecialchars($activity->owner_name); ?></strong>
                                            registered <em><?php echo htmlspecialchars($activity->vehicle_info); ?></em>
                                            for <a href="<?php echo BASE_URL; ?>/admin/registrations/<?php echo $activity->show_id; ?>">
                                                <?php echo htmlspecialchars($activity->show_name); ?>
                                            </a>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo timeAgo($activity->created_at); ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Upcoming Deadlines -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Upcoming Deadlines
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($upcoming_deadlines)): ?>
                        <p class="text-muted mb-0">No upcoming deadlines</p>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($upcoming_deadlines as $deadline): ?>
                                <div class="list-group-item px-0 py-2">
                                    <div class="small">
                                        <strong>
                                            <a href="<?php echo BASE_URL; ?>/admin/registrations/<?php echo $deadline->id; ?>">
                                                <?php echo htmlspecialchars($deadline->name); ?>
                                            </a>
                                        </strong>
                                        <br>
                                        <?php if ($deadline->days_to_reg_deadline >= 0 && $deadline->days_to_reg_deadline <= 7): ?>
                                            <span class="text-danger">
                                                <i class="fas fa-clock me-1"></i>
                                                Registration ends in <?php echo $deadline->days_to_reg_deadline; ?> day(s)
                                            </span>
                                        <?php elseif ($deadline->days_to_show >= 0 && $deadline->days_to_show <= 14): ?>
                                            <span class="text-warning">
                                                <i class="fas fa-calendar me-1"></i>
                                                Show starts in <?php echo $deadline->days_to_show; ?> day(s)
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for enhanced functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('showSearch');
    const searchButton = document.getElementById('searchButton');
    
    function performSearch() {
        const searchTerm = searchInput.value.trim();
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('search', searchTerm);
        currentUrl.searchParams.set('page', '1'); // Reset to first page
        window.location.href = currentUrl.toString();
    }
    
    searchButton.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // Per page selector
    const perPageSelect = document.getElementById('perPageSelect');
    perPageSelect.addEventListener('change', function() {
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('per_page', this.value);
        currentUrl.searchParams.set('page', '1'); // Reset to first page
        window.location.href = currentUrl.toString();
    });
    
    // Select all functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const showCheckboxes = document.querySelectorAll('.show-checkbox');
    const bulkActionsDropdown = document.getElementById('bulkActionsDropdown');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            showCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionsState();
        });
    }
    
    showCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionsState);
    });
    
    function updateBulkActionsState() {
        const checkedBoxes = document.querySelectorAll('.show-checkbox:checked');
        if (bulkActionsDropdown) {
            bulkActionsDropdown.disabled = checkedBoxes.length === 0;
        }
        
        // Update select all checkbox state
        if (selectAllCheckbox) {
            if (checkedBoxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedBoxes.length === showCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        }
    }
    
    // Bulk actions
    const bulkActionLinks = document.querySelectorAll('.bulk-action');
    const bulkActionForm = document.getElementById('bulkActionForm');
    const bulkActionInput = document.getElementById('bulkActionInput');
    
    bulkActionLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const action = this.dataset.action;
            const checkedBoxes = document.querySelectorAll('.show-checkbox:checked');
            
            if (checkedBoxes.length === 0) {
                alert('Please select at least one show.');
                return;
            }
            
            let confirmMessage = '';
            switch (action) {
                case 'activate':
                    confirmMessage = `Are you sure you want to activate ${checkedBoxes.length} show(s)?`;
                    break;
                case 'complete':
                    confirmMessage = `Are you sure you want to mark ${checkedBoxes.length} show(s) as completed?`;
                    break;
                case 'cancel':
                    confirmMessage = `Are you sure you want to cancel ${checkedBoxes.length} show(s)?`;
                    break;
                case 'delete':
                    confirmMessage = `Are you sure you want to DELETE ${checkedBoxes.length} show(s)? This action cannot be undone!`;
                    break;
            }
            
            if (confirm(confirmMessage)) {
                bulkActionInput.value = action;
                bulkActionForm.submit();
            }
        });
    });
    
    // Initialize bulk actions state
    updateBulkActionsState();
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>