<?php require APPROOT . '/views/includes/header.php'; ?>

<!-- Additional Photo-Specific Meta Tags -->
<meta property="og:image:alt" content="<?php echo htmlspecialchars($data['photo']['caption'] ?: $data['photo']['category_label']); ?>">
<meta property="og:image:type" content="image/jpeg">
<meta name="twitter:card" content="summary_large_image">

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Photo Display -->
            <div class="card shadow-sm">
                <div class="card-body p-0">
                    <!-- Photo Image -->
                    <div class="position-relative">
                        <img src="<?php echo $data['photo']['full_url']; ?>" 
                             alt="<?php echo htmlspecialchars($data['photo']['caption'] ?: $data['photo']['category_label']); ?>"
                             class="img-fluid w-100" 
                             style="max-height: 70vh; object-fit: contain; background: #f8f9fa;">
                        
                        <!-- Category Badge -->
                        <span class="position-absolute top-0 start-0 m-3">
                            <span class="badge bg-dark fs-6">
                                <?php echo htmlspecialchars($data['photo']['category_label']); ?>
                            </span>
                        </span>
                        
                        <!-- Privacy Badge -->
                        <?php if ($data['photo']['privacy_level'] !== 'public'): ?>
                            <span class="position-absolute top-0 end-0 m-3">
                                <span class="badge bg-warning">
                                    <i class="fas fa-lock me-1"></i>
                                    <?php echo ucfirst($data['photo']['privacy_level']); ?>
                                </span>
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Photo Details -->
                    <div class="p-4">
                        <!-- Caption -->
                        <?php if ($data['photo']['caption']): ?>
                            <div class="mb-3" style="white-space: pre-line; word-wrap: break-word;">
                                <h5 class="mb-2">Caption:</h5>
                                <p class="mb-0"><?php echo htmlspecialchars($data['photo']['caption']); ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Event Info -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-2"></i>
                                    <strong>Event:</strong> <?php echo htmlspecialchars($data['photo']['event_name']); ?>
                                </small>
                                <?php if ($data['photo']['event_date']): ?>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-2"></i>
                                        <?php echo date('F j, Y', strtotime($data['photo']['event_date'])); ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-camera me-2"></i>
                                    <strong>By:</strong> <?php echo htmlspecialchars($data['photo']['photographer_name']); ?>
                                </small>
                                <br>
                                <small class="text-muted">
                                    <i class="fas fa-upload me-2"></i>
                                    <?php echo timeAgo($data['photo']['created_at']); ?>
                                </small>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="d-flex flex-wrap gap-2">
                            <!-- Facebook Share -->
                            <button class="btn btn-primary" onclick="shareToFacebook()">
                                <i class="fab fa-facebook-f me-2"></i>Share on Facebook
                            </button>
                            
                            <!-- Copy Link -->
                            <button class="btn btn-outline-secondary" onclick="copyPhotoLink()">
                                <i class="fas fa-link me-2"></i>Copy Link
                            </button>
                            
                            <!-- View Event Gallery -->
                            <?php if ($data['photo']['event_name'] !== 'Event Photo'): ?>
                                <a href="<?php echo BASE_URL; ?>/image_editor/eventGallery/<?php echo $data['photo']['event_type'] ?? 'event'; ?>/<?php echo $data['photo']['event_id'] ?? ''; ?>" 
                                   class="btn btn-outline-info">
                                    <i class="fas fa-images me-2"></i>View Event Gallery
                                </a>
                            <?php endif; ?>
                            
                            <!-- Back to Site -->
                            <a href="<?php echo BASE_URL; ?>" class="btn btn-outline-dark">
                                <i class="fas fa-home me-2"></i>Back to <?php echo APP_NAME; ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Site Promotion -->
            <div class="card mt-4 bg-light">
                <div class="card-body text-center">
                    <h6 class="mb-2">
                        <i class="fas fa-star text-warning me-2"></i>
                        Discover More Amazing Event Photos
                    </h6>
                    <p class="text-muted mb-3">
                        Join <?php echo APP_NAME; ?> to share your own photos and connect with fellow car enthusiasts!
                    </p>
                    <a href="<?php echo BASE_URL; ?>" class="btn btn-primary">
                        <i class="fas fa-external-link-alt me-2"></i>Visit <?php echo APP_NAME; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Facebook sharing function
function shareToFacebook() {
    const photoData = {
        url: '<?php echo $data['og_data']['url']; ?>',
        title: '<?php echo addslashes($data['og_data']['title']); ?>',
        description: '<?php echo addslashes($data['og_data']['description']); ?>',
        image: '<?php echo $data['og_data']['image']; ?>'
    };
    
    // Use Facebook SDK if available, otherwise fallback to simple share
    if (typeof FB !== 'undefined') {
        FB.ui({
            method: 'share',
            href: photoData.url,
            quote: photoData.description
        }, function(response) {
            if (response && !response.error_message) {
                console.log('Photo shared successfully');
            }
        });
    } else {
        // Fallback to Facebook share URL
        const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(photoData.url)}`;
        window.open(shareUrl, 'facebook-share', 'width=580,height=296');
    }
}

// Copy photo link to clipboard
function copyPhotoLink() {
    const photoUrl = '<?php echo $data['og_data']['url']; ?>';
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(photoUrl).then(function() {
            showNotification('Link copied to clipboard!', 'success');
        }).catch(function() {
            fallbackCopyTextToClipboard(photoUrl);
        });
    } else {
        fallbackCopyTextToClipboard(photoUrl);
    }
}

// Fallback copy function for older browsers
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showNotification('Link copied to clipboard!', 'success');
    } catch (err) {
        showNotification('Failed to copy link', 'error');
    }
    
    document.body.removeChild(textArea);
}

// Simple notification function
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
