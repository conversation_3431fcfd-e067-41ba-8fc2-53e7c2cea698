<?php
// Quick test to see what photos exist in the database
require_once 'config/config.php';
require_once 'core/Database.php';

$db = new Database();

echo "<h1>Event Photos in Database</h1>";

// Get all event photos
$db->query('SELECT i.id, i.filename, i.file_path, i.entity_type, i.created_at,
           epm.event_type, epm.event_id, epm.category, epm.caption
           FROM images i
           LEFT JOIN event_photo_metadata epm ON i.id = epm.image_id
           WHERE i.entity_type = "event_photo"
           ORDER BY i.created_at DESC
           LIMIT 10');

$photos = $db->resultSet();

if (empty($photos)) {
    echo "<p>No event photos found in database.</p>";
    
    // Check if there are any images at all
    $db->query('SELECT COUNT(*) as count FROM images');
    $result = $db->single();
    echo "<p>Total images in database: " . $result->count . "</p>";
    
    // Show some sample images
    $db->query('SELECT id, filename, file_path, entity_type FROM images LIMIT 5');
    $allImages = $db->resultSet();
    
    if (!empty($allImages)) {
        echo "<h2>Sample Images (any type):</h2>";
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Filename</th><th>File Path</th><th>Entity Type</th></tr>";
        foreach ($allImages as $img) {
            echo "<tr>";
            echo "<td>" . $img->id . "</td>";
            echo "<td>" . htmlspecialchars($img->filename) . "</td>";
            echo "<td>" . htmlspecialchars($img->file_path) . "</td>";
            echo "<td>" . htmlspecialchars($img->entity_type) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Filename</th><th>File Path</th><th>Event Type</th><th>Event ID</th><th>Category</th><th>Caption</th><th>Test Links</th></tr>";
    
    foreach ($photos as $photo) {
        echo "<tr>";
        echo "<td>" . $photo->id . "</td>";
        echo "<td>" . htmlspecialchars($photo->filename) . "</td>";
        echo "<td>" . htmlspecialchars($photo->file_path) . "</td>";
        echo "<td>" . htmlspecialchars($photo->event_type) . "</td>";
        echo "<td>" . $photo->event_id . "</td>";
        echo "<td>" . htmlspecialchars($photo->category) . "</td>";
        echo "<td>" . htmlspecialchars(substr($photo->caption ?? '', 0, 50)) . "</td>";
        echo "<td>";
        echo "<a href='/photo/debug/" . $photo->id . "' target='_blank'>Debug</a> | ";
        echo "<a href='/photo/share/" . $photo->id . "' target='_blank'>Share</a> | ";
        echo "<a href='/" . $photo->file_path . "' target='_blank'>Image</a>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>Test URLs:</h2>";
    if (!empty($photos)) {
        $firstPhoto = $photos[0];
        echo "<p><strong>Debug URL:</strong> <a href='/photo/debug/" . $firstPhoto->id . "'>/photo/debug/" . $firstPhoto->id . "</a></p>";
        echo "<p><strong>Share URL:</strong> <a href='/photo/share/" . $firstPhoto->id . "'>/photo/share/" . $firstPhoto->id . "</a></p>";
        echo "<p><strong>Image URL:</strong> <a href='/" . $firstPhoto->file_path . "'>/" . $firstPhoto->file_path . "</a></p>";
        echo "<p><strong>Facebook Debug:</strong> <a href='https://developers.facebook.com/tools/debug/?q=" . urlencode(BASE_URL . '/photo/share/' . $firstPhoto->id) . "' target='_blank'>Test in Facebook</a></p>";
    }
}
?>
