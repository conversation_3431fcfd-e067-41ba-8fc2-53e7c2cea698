<?php
/**
 * Test Calendar Clubs Popup Enhancement
 * 
 * This script tests the calendar clubs popup functionality
 * to ensure the database query and data formatting work correctly.
 */

// Include the necessary files
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/CalendarModel.php';

// Set debug mode for testing
if (!defined('DEBUG_MODE')) {
    define('DEBUG_MODE', true);
}

echo "<h1>Calendar Clubs Popup Test</h1>\n";

try {
    // Initialize the calendar model
    $calendarModel = new CalendarModel();
    
    echo "<h2>Testing Calendar Events with Clubs</h2>\n";
    
    // Get events with no filters to see all events
    $events = $calendarModel->getEvents([], null);
    
    echo "<p>Found " . count($events) . " events</p>\n";
    
    if (count($events) > 0) {
        echo "<h3>Events with Club Information:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>Event ID</th><th>Title</th><th>Hosting Clubs</th><th>Parsed Clubs</th></tr>\n";
        
        foreach ($events as $event) {
            $hostingClubs = $event->hosting_clubs ?? '';
            $parsedClubs = [];
            
            if ($hostingClubs && trim($hostingClubs) !== '') {
                $clubs = explode('||', $hostingClubs);
                foreach ($clubs as $clubData) {
                    if (trim($clubData) !== '') {
                        $parts = explode(':', $clubData);
                        if (count($parts) >= 2) {
                            $parsedClubs[] = [
                                'id' => $parts[0],
                                'name' => $parts[1]
                            ];
                        }
                    }
                }
            }
            
            echo "<tr>\n";
            echo "<td>" . htmlspecialchars($event->id) . "</td>\n";
            echo "<td>" . htmlspecialchars($event->title) . "</td>\n";
            echo "<td>" . htmlspecialchars($hostingClubs) . "</td>\n";
            echo "<td>";
            if (count($parsedClubs) > 0) {
                foreach ($parsedClubs as $club) {
                    echo "<span style='background: #f8f9fa; padding: 2px 6px; margin: 2px; border-radius: 10px; border: 1px solid #dee2e6; font-size: 0.8em;'>";
                    echo htmlspecialchars($club['name']);
                    echo "</span> ";
                }
            } else {
                echo "<em>No clubs</em>";
            }
            echo "</td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    } else {
        echo "<p><em>No events found in the database.</em></p>\n";
    }
    
    // Test the database structure
    echo "<h2>Database Structure Test</h2>\n";
    
    $db = new Database();
    
    // Check if required tables exist
    $tables = ['calendar_events', 'calendar_clubs', 'calendar_event_clubs'];
    foreach ($tables as $table) {
        $db->query("SHOW TABLES LIKE '$table'");
        $db->execute();
        if ($db->rowCount() > 0) {
            echo "<p>✅ Table '$table' exists</p>\n";
        } else {
            echo "<p>❌ Table '$table' does not exist</p>\n";
        }
    }
    
    // Check for sample data
    echo "<h3>Sample Data Check:</h3>\n";
    
    $db->query("SELECT COUNT(*) as count FROM calendar_events");
    $db->execute();
    $eventCount = $db->single()->count;
    echo "<p>Calendar Events: $eventCount</p>\n";
    
    $db->query("SELECT COUNT(*) as count FROM calendar_clubs");
    $db->execute();
    $clubCount = $db->single()->count;
    echo "<p>Calendar Clubs: $clubCount</p>\n";
    
    $db->query("SELECT COUNT(*) as count FROM calendar_event_clubs");
    $db->execute();
    $relationCount = $db->single()->count;
    echo "<p>Event-Club Relations: $relationCount</p>\n";
    
    if ($relationCount > 0) {
        echo "<h3>Sample Event-Club Relations:</h3>\n";
        $db->query("
            SELECT e.id, e.title, c.name as club_name 
            FROM calendar_events e 
            JOIN calendar_event_clubs ec ON e.id = ec.event_id 
            JOIN calendar_clubs c ON ec.club_id = c.id 
            LIMIT 5
        ");
        $db->execute();
        $relations = $db->resultSet();
        
        if ($relations) {
            echo "<ul>\n";
            foreach ($relations as $relation) {
                echo "<li>Event: " . htmlspecialchars($relation->title) . " → Club: " . htmlspecialchars($relation->club_name) . "</li>\n";
            }
            echo "</ul>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<h2>Test Complete</h2>\n";
echo "<p><a href='/calendar'>← Back to Calendar</a></p>\n";
?>