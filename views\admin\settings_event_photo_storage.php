<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-database me-2"></i>
                        Event Photo Storage & Cleanup Settings
                    </h1>
                    <p class="text-muted">Configure storage limits, retention policies, and automatic cleanup</p>
                </div>
                <div>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_event_photos" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Event Photos
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-hdd me-2"></i>
                        Storage & Cleanup Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo BASE_URL; ?>/admin/event_photo_admin_storage">
                        <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token'] ?? ''; ?>">
                        
                        <!-- Storage Limits -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-warning mb-3">
                                    <i class="fas fa-hdd me-2"></i>
                                    Storage Limits
                                </h6>
                            </div>
                            <div class="col-md-4">
                                <label for="max_storage_gb" class="form-label">Maximum Storage</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="max_storage_gb" name="max_storage_gb" 
                                           value="<?php echo $data['settings']['max_storage_gb'] ?? 50; ?>" 
                                           min="1" max="1000" required>
                                    <span class="input-group-text">GB</span>
                                </div>
                                <small class="text-muted">Maximum total storage for all event photos</small>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch mt-4" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                    <input class="form-check-input" type="checkbox" id="auto_cleanup_enabled" name="auto_cleanup_enabled"
                                           style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                           <?php echo ($data['settings']['auto_cleanup_enabled'] ?? false) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="auto_cleanup_enabled">
                                        <i class="fas fa-broom me-2 text-warning"></i>
                                        <strong>Enable Automatic Cleanup</strong>
                                        <small class="d-block text-muted">Automatically remove old photos based on engagement</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="cleanup_frequency_days" class="form-label">Cleanup Frequency</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="cleanup_frequency_days" name="cleanup_frequency_days" 
                                           value="<?php echo $data['settings']['cleanup_frequency_days'] ?? 30; ?>" 
                                           min="1" max="365" required>
                                    <span class="input-group-text">days</span>
                                </div>
                                <small class="text-muted">How often to run automatic cleanup</small>
                            </div>
                        </div>

                        <!-- Engagement Thresholds -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-warning mb-3">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Engagement Thresholds
                                </h6>
                                <p class="text-muted">Photos are categorized by engagement (views, likes, shares) to determine retention periods</p>
                            </div>
                            <div class="col-md-4">
                                <label for="high_engagement_threshold" class="form-label">High Engagement Threshold</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="high_engagement_threshold" name="high_engagement_threshold" 
                                           value="<?php echo $data['settings']['high_engagement_threshold'] ?? 10; ?>" 
                                           min="1" max="1000" required>
                                    <span class="input-group-text">interactions</span>
                                </div>
                                <small class="text-muted">Photos with this many interactions are considered high engagement</small>
                            </div>
                            <div class="col-md-4">
                                <label for="medium_engagement_threshold" class="form-label">Medium Engagement Threshold</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="medium_engagement_threshold" name="medium_engagement_threshold" 
                                           value="<?php echo $data['settings']['medium_engagement_threshold'] ?? 3; ?>" 
                                           min="1" max="100" required>
                                    <span class="input-group-text">interactions</span>
                                </div>
                                <small class="text-muted">Photos with this many interactions are considered medium engagement</small>
                            </div>
                        </div>

                        <!-- Retention Periods -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-warning mb-3">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    Retention Periods
                                </h6>
                                <p class="text-muted">How long to keep photos based on their engagement level</p>
                            </div>
                            <div class="col-md-4">
                                <label for="high_engagement_retention_months" class="form-label">High Engagement Retention</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="high_engagement_retention_months" name="high_engagement_retention_months" 
                                           value="<?php echo $data['settings']['high_engagement_retention_months'] ?? 24; ?>" 
                                           min="1" max="120" required>
                                    <span class="input-group-text">months</span>
                                </div>
                                <small class="text-muted">Keep high engagement photos for this long</small>
                            </div>
                            <div class="col-md-4">
                                <label for="medium_engagement_retention_months" class="form-label">Medium Engagement Retention</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="medium_engagement_retention_months" name="medium_engagement_retention_months" 
                                           value="<?php echo $data['settings']['medium_engagement_retention_months'] ?? 12; ?>" 
                                           min="1" max="60" required>
                                    <span class="input-group-text">months</span>
                                </div>
                                <small class="text-muted">Keep medium engagement photos for this long</small>
                            </div>
                            <div class="col-md-4">
                                <label for="low_engagement_retention_months" class="form-label">Low Engagement Retention</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="low_engagement_retention_months" name="low_engagement_retention_months" 
                                           value="<?php echo $data['settings']['low_engagement_retention_months'] ?? 6; ?>" 
                                           min="1" max="24" required>
                                    <span class="input-group-text">months</span>
                                </div>
                                <small class="text-muted">Keep low engagement photos for this long</small>
                            </div>
                        </div>

                        <!-- Warning Notice -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        Important Storage Information
                                    </h6>
                                    <ul class="mb-0">
                                        <li><strong>Automatic cleanup</strong> permanently deletes photos that exceed retention periods</li>
                                        <li><strong>High engagement photos</strong> are preserved longer due to their popularity</li>
                                        <li><strong>Storage limits</strong> help prevent server disk space issues</li>
                                        <li><strong>Cleanup frequency</strong> determines how often the system checks for old photos</li>
                                        <li><strong>Deleted photos cannot be recovered</strong> - ensure backup systems are in place</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Manual Actions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-warning mb-3">
                                    <i class="fas fa-tools me-2"></i>
                                    Manual Actions
                                </h6>
                                <p class="text-muted">Run cleanup and maintenance tasks manually</p>
                            </div>
                            <div class="col-md-4">
                                <a href="<?php echo BASE_URL; ?>/admin/runEventPhotoCleanup?dry_run=1"
                                   class="btn btn-outline-warning w-100 mb-2"
                                   onclick="return confirm('Run cleanup in test mode? No files will be deleted.')">
                                    <i class="fas fa-search me-2"></i>
                                    Test Cleanup (Dry Run)
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="<?php echo BASE_URL; ?>/admin/runEventPhotoCleanup"
                                   class="btn btn-warning w-100 mb-2"
                                   onclick="return confirm('Run live cleanup? This will permanently delete expired photos!')">
                                    <i class="fas fa-trash me-2"></i>
                                    Run Live Cleanup
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="<?php echo BASE_URL; ?>/admin/runEventPhotoStats"
                                   class="btn btn-outline-info w-100 mb-2">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Update Statistics
                                </a>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <button type="submit" class="btn btn-warning btn-lg text-dark">
                                    <i class="fas fa-save me-2"></i>
                                    Save Storage Settings
                                </button>
                                <a href="<?php echo BASE_URL; ?>/admin/settings_event_photos" class="btn btn-outline-secondary btn-lg ms-2">
                                    <i class="fas fa-times me-2"></i>
                                    Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-check-input:checked {
    background-color: #ffc107;
    border-color: #ffc107;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.text-warning {
    color: #856404 !important;
}

.input-group-text {
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.form-control:focus, .form-select:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
