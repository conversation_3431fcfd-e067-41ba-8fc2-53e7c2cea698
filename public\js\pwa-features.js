/**
 * PWA Features Module for Rowan Elite Rides Events & Shows
 * Handles installation, notifications, offline functionality, and enhanced mobile features
 */

// Prevent duplicate loading
if (typeof window.PWAFeatures !== 'undefined') {
    console.warn('PWAFeatures already loaded, skipping duplicate initialization');
} else {

class PWAFeatures {
    constructor() {
        this.version = '3.64.2-cache-mgmt-pwa-update';
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.notificationPermission = 'default';
        console.log('[PWA] PWAFeatures loaded - version:', this.version);
        this.isOnline = navigator.onLine;
        this.db = null;
        
        // Camera/QR Scanner state management
        this.currentStream = null;
        this.currentModal = null;

        // Audio system for QR beep sounds
        this.audioContext = null;
        this.audioInitialized = false;
        
        this.init();
    }
    
    async init() {
        console.log('[PWA] Initializing PWA features...');
        
        // Register service worker
        await this.registerServiceWorker();
        
        // Initialize IndexedDB
        await this.initIndexedDB();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Check installation status
        this.checkInstallationStatus();
        
        // Initialize push notifications
        this.initPushNotifications();
        
        // Setup offline handling - DISABLED
        // this.setupOfflineHandling();
        
        // Initialize enhanced mobile features
        this.initMobileFeatures();

        // Check for forced update after cache clearing
        console.log('[PWA] About to check for forced update...');
        this.checkForcedUpdate();

        // Update banners not needed for 100% dynamic site

        console.log('[PWA] PWA features initialized');
        
        // Clear update flags if page loaded successfully (indicates update was applied)
        this.clearUpdateFlagsIfNeeded();
        
        // Simple check for PWA update banner after cache clearing
        console.log('[PWA] Setting up update banner check in 3 seconds...');
        setTimeout(() => {
            console.log('[PWA] 3 seconds elapsed, checking for update banner flag now...');
            this.checkForUpdateBannerFlag();
        }, 3000); // Wait 3 seconds for everything to load
        
        // Add global test functions for debugging
        window.testPWABanner = () => {
            console.log('[PWA] Manual test - showing update banner');
            this.showUpdateBanner(true);
        };
        
        window.testPWAFlag = () => {
            console.log('[PWA] Manual test - setting flag and checking');
            sessionStorage.setItem('show_pwa_update_banner', 'true');
            console.log('[PWA] Flag set:', sessionStorage.getItem('show_pwa_update_banner'));
            this.checkForUpdateBannerFlag();
        };
        
        // Temporary: Add global test function
        window.testPWAUpdateBanner = () => {
            console.log('[PWA] Manual test of update banner');
            this.showUpdateBanner(true);
        };
    }
    
    // Clear update flags if page loaded successfully after an update
    clearUpdateFlagsIfNeeded() {
        const updateApplied = sessionStorage.getItem('pwa_update_applied');
        if (updateApplied === 'true') {
            console.log('[PWA] Page loaded after update, clearing update flags');
            sessionStorage.removeItem('pwa_update_applied');
            sessionStorage.removeItem('pwa_update_dismissed');
            // Keep pwa_last_update_shown to prevent immediate re-showing
        }
    }
    
    // Service Worker Registration with Update Detection
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js', {
                    scope: '/'
                });

                console.log('[PWA] Service Worker registered:', registration.scope);
                console.log('[PWA] Caching disabled - all content will be fetched fresh');

                // Set up update detection
                this.setupUpdateDetection(registration);

                return registration;
            } catch (error) {
                console.error('[PWA] Service Worker registration failed:', error);
            }
        }
    }

    // Setup service worker update detection
    setupUpdateDetection(registration) {
        // Check for updates on registration
        registration.addEventListener('updatefound', () => {
            console.log('[PWA] Service worker update found');
            const newWorker = registration.installing;
            
            newWorker.addEventListener('statechange', () => {
                console.log('[PWA] New service worker state:', newWorker.state);
                
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    console.log('[PWA] New service worker installed, showing update banner');
                    this.showUpdateBanner();
                }
            });
        });

        // Listen for controller changes (when new SW takes control)
        navigator.serviceWorker.addEventListener('controllerchange', () => {
            console.log('[PWA] Service worker controller changed');
            // Don't reload automatically, let user choose
        });

        // Check for waiting service worker on page load
        if (registration.waiting) {
            console.log('[PWA] Service worker waiting on page load');
            this.showUpdateBanner();
        }

        // Periodic update checks (every 2 hours, less aggressive)
        setInterval(() => {
            // Only check if no update is currently being processed
            const updateApplied = sessionStorage.getItem('pwa_update_applied');
            const dismissed = sessionStorage.getItem('pwa_update_dismissed');
            
            if (updateApplied !== 'true' && dismissed !== 'true') {
                console.log('[PWA] Checking for service worker updates...');
                registration.update().catch(err => {
                    console.log('[PWA] Update check failed:', err);
                });
            } else {
                console.log('[PWA] Skipping update check (update recently applied or dismissed)');
            }
        }, 2 * 60 * 60 * 1000); // 2 hours
    }
    
    // Check if database connection is healthy
    isDatabaseHealthy() {
        return this.db && 
               this.db.readyState !== 'closed' && 
               !this.db.objectStoreNames.contains === undefined;
    }
    
    // IndexedDB Initialization
    async initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('REREventsDB', 1);
            
            request.onerror = () => {
                console.error('[PWA] IndexedDB failed to open');
                reject(request.error);
            };
            
            request.onsuccess = () => {
                this.db = request.result;
                
                // Add error handler for database connection issues
                this.db.onerror = (event) => {
                    console.error('[PWA] Database error:', event.target.error);
                };
                
                this.db.onclose = () => {
                    console.warn('[PWA] Database connection closed unexpectedly');
                    this.db = null;
                };
                
                console.log('[PWA] IndexedDB initialized');
                resolve(this.db);
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores for offline data
                if (!db.objectStoreNames.contains('pendingRegistrations')) {
                    db.createObjectStore('pendingRegistrations', { keyPath: 'id' });
                }
                if (!db.objectStoreNames.contains('pendingPayments')) {
                    db.createObjectStore('pendingPayments', { keyPath: 'id' });
                }
                if (!db.objectStoreNames.contains('pendingScores')) {
                    db.createObjectStore('pendingScores', { keyPath: 'id' });
                }
                if (!db.objectStoreNames.contains('cachedData')) {
                    db.createObjectStore('cachedData', { keyPath: 'key' });
                }
                if (!db.objectStoreNames.contains('offlineQueue')) {
                    db.createObjectStore('offlineQueue', { keyPath: 'id', autoIncrement: true });
                }
            };
        });
    }
    
    // Event Listeners Setup
    setupEventListeners() {
        // Install prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
        });
        
        // App installed
        window.addEventListener('appinstalled', () => {
            console.log('[PWA] App installed');
            this.isInstalled = true;
            this.hideInstallButton();
            this.showWelcomeMessage();
        });
        
        // Online/Offline status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.handleOnlineStatus();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.handleOfflineStatus();
        });
        
        // Visibility change for background sync and modal cleanup
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isOnline) {
                this.syncPendingData();
            }
            
            // Force close camera/QR modals when app becomes visible again
            // This helps with PWA modal issues where modals might get stuck
            if (!document.hidden) {
                // Small delay to ensure proper cleanup
                setTimeout(() => {
                    const hasOpenModals = document.querySelector('.camera-modal') || document.querySelector('.qr-scanner-modal');
                    if (hasOpenModals && this.currentStream) {
                        console.log('[PWA] Detected stuck modals on visibility change, force closing');
                        this.forceCloseAllModals();
                    }
                }, 100);
            }
        });
        
        // Page unload handler to clean up camera streams
        window.addEventListener('beforeunload', () => {
            if (this.currentStream) {
                console.log('[PWA] Page unloading, cleaning up camera streams');
                this.forceCloseAllModals();
            }
        });
        
        // Page hide handler for mobile PWA
        window.addEventListener('pagehide', () => {
            if (this.currentStream) {
                console.log('[PWA] Page hiding, cleaning up camera streams');
                this.forceCloseAllModals();
            }
        });
    }
    
    // Installation Management
    checkInstallationStatus() {
        // Check if running as installed app
        if (window.matchMedia('(display-mode: standalone)').matches || 
            window.navigator.standalone === true) {
            this.isInstalled = true;
            console.log('[PWA] Running as installed app');
        }
    }
    
    showInstallButton() {
        const installButton = document.getElementById('pwa-install-button');
        if (installButton) {
            installButton.style.display = 'block';
            // Only add listener if not already added
            if (!installButton.hasAttribute('data-listener-added')) {
                installButton.addEventListener('click', () => this.promptInstall());
                installButton.setAttribute('data-listener-added', 'true');
            }
        } else {
            // Create install button if it doesn't exist
            this.createInstallButton();
        }
    }
    
    createInstallButton() {
        const button = document.createElement('button');
        button.id = 'pwa-install-button';
        button.className = 'btn btn-primary pwa-install-btn';
        button.innerHTML = '<i class="fas fa-download"></i> Install App';
        button.addEventListener('click', () => this.promptInstall());
        button.setAttribute('data-listener-added', 'true');

        // Add to navigation or header
        const nav = document.querySelector('.navbar') || document.querySelector('header');
        if (nav) {
            nav.appendChild(button);
        }
    }
    
    promptInstall() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            this.deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    console.log('[PWA] User accepted install prompt');
                } else {
                    console.log('[PWA] User dismissed install prompt');
                }
                this.deferredPrompt = null;
            });
        }
    }
    
    hideInstallButton() {
        const installButton = document.getElementById('pwa-install-button');
        if (installButton) {
            installButton.style.display = 'none';
        }
    }
    
    showWelcomeMessage() {
        this.showNotification('Welcome!', 'App installed successfully. You can now access RER Events from your home screen!', 'success');
    }
    
    // Push Notifications
    async initPushNotifications() {
        // DISABLED: Old Web Push API system - now using FCM OAuth v1 instead
        console.log('[PWA] Push notifications handled by FCM system (fcm-notifications.js)');
        return;
        
        /* OLD WEB PUSH CODE DISABLED
        // Only initialize push notifications if user is logged in
        if (!window.PWA_CONFIG || !window.PWA_CONFIG.userId) {
            console.log('[PWA] Skipping push notifications - user not logged in');
            return;
        }

        if ('Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window) {
            this.notificationPermission = Notification.permission;

            if (this.notificationPermission === 'default') {
                // Check if user has enabled push notifications in their preferences
                this.checkUserPushPreference();
            } else if (this.notificationPermission === 'granted') {
                await this.subscribeToPush();
            }
        }
        */
    }

    async checkUserPushPreference() {
        try {
            const response = await fetch(`${window.BASE_URL}/notification/getUserPreferences`);
            const data = await response.json();

            if (data.success && data.preferences && data.preferences.push_notifications) {
                console.log('[PWA] User has enabled push notifications in preferences, requesting permission');
                this.showNotificationPrompt();
            } else {
                console.log('[PWA] User has not enabled push notifications in preferences');
            }
        } catch (error) {
            console.error('[PWA] Error checking user push preference:', error);
            // Fallback to showing prompt anyway
            this.showNotificationPrompt();
        }
    }
    
    showNotificationPrompt() {
        const promptDiv = document.createElement('div');
        promptDiv.className = 'notification-prompt alert alert-info';
        promptDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>Stay Updated!</strong> Enable notifications for event reminders and updates.
                </div>
                <div>
                    <button class="btn btn-sm btn-primary me-2" onclick="pwaFeatures.requestNotificationPermission()">
                        <i class="fas fa-bell"></i> Enable
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">
                        Later
                    </button>
                </div>
            </div>
        `;
        
        document.body.insertBefore(promptDiv, document.body.firstChild);
    }
    
    async requestNotificationPermission() {
        // Check if user is logged in before requesting permission
        if (!window.PWA_CONFIG || !window.PWA_CONFIG.userId) {
            console.log('[PWA] Cannot request notification permission - user not logged in');
            this.showNotification('Login Required', 'Please log in to enable notifications', 'warning');
            return;
        }

        try {
            // Use FCM manager if available
            if (window.fcmManager) {
                const token = await window.fcmManager.requestPermissionAndGetToken();
                if (token) {
                    this.showNotification('Notifications Enabled', 'You\'ll now receive important updates and reminders!', 'success');
                }
            } else {
                // Fallback to old method
                const permission = await Notification.requestPermission();
                this.notificationPermission = permission;

                if (permission === 'granted') {
                    await this.subscribeToPush();
                    this.showNotification('Notifications Enabled', 'You\'ll now receive important updates and reminders!', 'success');
                }
            }

            // Remove prompt
            const prompt = document.querySelector('.notification-prompt');
            if (prompt) prompt.remove();

        } catch (error) {
            console.error('[PWA] Notification permission request failed:', error);
        }
    }
    
    async subscribeToPush() {
        try {
            if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
                throw new Error('Push notifications not supported');
            }
            
            const registration = await navigator.serviceWorker.ready;
            
            if (!registration || !registration.pushManager) {
                throw new Error('Service worker registration does not support push notifications');
            }
            
            // Check if already subscribed
            let subscription = await registration.pushManager.getSubscription();
            
            if (!subscription) {
                // Get VAPID public key
                const vapidKey = await this.getVAPIDPublicKey();
                
                if (!vapidKey) {
                    throw new Error('VAPID public key not available');
                }
                
                // Subscribe to push notifications
                subscription = await registration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: this.urlBase64ToUint8Array(vapidKey)
                });
            }
            
            // Send subscription to server
            await this.sendSubscriptionToServer(subscription);
            
            console.log('[PWA] Push subscription successful');
            
        } catch (error) {
            console.error('[PWA] Push subscription failed:', error);
        }
    }
    
    async getVAPIDPublicKey() {
        try {
            const response = await fetch('/api/notifications/vapid-key');
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data.success || !data.publicKey) {
                throw new Error('Invalid VAPID key response');
            }
            
            return data.publicKey;
        } catch (error) {
            console.error('[PWA] Failed to get VAPID key:', error);
            // Return null instead of fallback key to prevent invalid subscriptions
            return null;
        }
    }
    
    async sendSubscriptionToServer(subscription) {
        try {
            const response = await fetch('/api/notifications/subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    subscription: subscription.toJSON(),
                    userAgent: navigator.userAgent,
                    timestamp: new Date().toISOString()
                })
            });

            if (!response.ok) {
                throw new Error('Failed to send subscription to server');
            }

        } catch (error) {
            console.error('[PWA] Failed to send subscription to server:', error);
        }
    }

    /**
     * Enable push notifications after user login
     * This method can be called from login success handlers
     */
    async enablePushNotificationsAfterLogin() {
        console.log('[PWA] Enabling push notifications after login');

        if ('Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window) {
            this.notificationPermission = Notification.permission;

            if (this.notificationPermission === 'default') {
                this.showNotificationPrompt();
            } else if (this.notificationPermission === 'granted') {
                await this.subscribeToPush();
            }
        }
    }
    
    urlBase64ToUint8Array(base64String) {
        if (!base64String || typeof base64String !== 'string') {
            throw new Error('Invalid base64 string provided to urlBase64ToUint8Array');
        }
        
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');
        
        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);
        
        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }
    
    // Offline Functionality
    setupOfflineHandling() {
        // Show offline indicator
        this.updateOnlineStatus();
        
        // Intercept form submissions for offline handling
        this.interceptFormSubmissions();
        
        // Setup periodic sync attempts
        setInterval(() => {
            if (this.isOnline) {
                this.syncPendingData();
            }
        }, 30000); // Every 30 seconds
    }
    
    handleOnlineStatus() {
        console.log('[PWA] Back online');
        this.updateOnlineStatus();
        this.syncPendingData();
        this.showNotification('Back Online', 'Syncing your offline actions...', 'success');
    }
    
    handleOfflineStatus() {
        console.log('[PWA] Gone offline');
        this.updateOnlineStatus();
        this.showNotification('Offline Mode', 'You can continue using the app. Changes will sync when you\'re back online.', 'warning');
    }
    
    updateOnlineStatus() {
        // Online status indicator disabled
        return;
    }

    createOnlineStatusIndicator() {
        // Online status indicator disabled
        return;
    }
    
    interceptFormSubmissions() {
        document.addEventListener('submit', async (event) => {
            if (!this.isOnline) {
                event.preventDefault();
                await this.handleOfflineFormSubmission(event.target);
            }
        });
    }
    
    async handleOfflineFormSubmission(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // Determine form type and handle accordingly
        const formType = this.determineFormType(form);
        
        if (formType) {
            await this.queueOfflineAction(formType, data);
            this.showNotification('Saved Offline', 'Your submission will be processed when you\'re back online.', 'info');
        }
    }
    
    determineFormType(form) {
        const action = form.action || '';
        const className = form.className || '';
        
        if (action.includes('registration') || className.includes('registration')) {
            return 'registration';
        } else if (action.includes('payment') || className.includes('payment')) {
            return 'payment';
        } else if (action.includes('score') || className.includes('judging')) {
            return 'scoring';
        }
        
        return 'general';
    }
    
    async queueOfflineAction(type, data) {
        // Ensure we have a healthy database connection
        if (!this.isDatabaseHealthy()) {
            console.log('[PWA] Database unhealthy, reinitializing for queue operation...');
            try {
                await this.initIndexedDB();
            } catch (error) {
                console.error('[PWA] Failed to reinitialize database for queuing:', error);
                return;
            }
        }
        
        try {
            await new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['offlineQueue'], 'readwrite');
                const store = transaction.objectStore('offlineQueue');
                
                const queueItem = {
                    type: type,
                    data: data,
                    timestamp: new Date().toISOString(),
                    synced: false
                };
                
                const request = store.add(queueItem);
                
                transaction.oncomplete = () => {
                    console.log('[PWA] Item queued successfully:', type);
                    resolve();
                };
                
                transaction.onerror = () => reject(transaction.error);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('[PWA] Failed to queue offline action:', error);
        }
    }
    
    async syncPendingData() {
        if (!this.isOnline) {
            console.log('[PWA] Offline - skipping sync');
            return;
        }
        
        // Ensure we have a valid database connection
        if (!this.isDatabaseHealthy()) {
            console.log('[PWA] Database connection unhealthy, reinitializing...');
            try {
                await this.initIndexedDB();
            } catch (error) {
                console.error('[PWA] Failed to reinitialize database:', error);
                return;
            }
        }
        
        try {
            // Use a promise-based approach for better error handling
            const pendingItems = await new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['offlineQueue'], 'readonly');
                const store = transaction.objectStore('offlineQueue');
                const request = store.getAll();
                
                transaction.onerror = () => reject(transaction.error);
                transaction.onabort = () => reject(new Error('Transaction aborted'));
                
                request.onsuccess = () => {
                    const items = request.result.filter(item => !item.synced);
                    resolve(items);
                };
                
                request.onerror = () => reject(request.error);
            });
            
            if (pendingItems.length === 0) {
                console.log('[PWA] No pending items to sync');
                return;
            }
            
            console.log(`[PWA] Syncing ${pendingItems.length} pending items`);
            
            for (const item of pendingItems) {
                try {
                    await this.syncItem(item);
                    
                    // Mark as synced in a new transaction
                    await new Promise((resolve, reject) => {
                        const updateTransaction = this.db.transaction(['offlineQueue'], 'readwrite');
                        const updateStore = updateTransaction.objectStore('offlineQueue');
                        
                        item.synced = true;
                        const updateRequest = updateStore.put(item);
                        
                        updateTransaction.oncomplete = () => resolve();
                        updateTransaction.onerror = () => reject(updateTransaction.error);
                        updateRequest.onerror = () => reject(updateRequest.error);
                    });
                    
                    console.log('[PWA] Item synced successfully:', item.id);
                    
                } catch (error) {
                    console.error('[PWA] Failed to sync item:', error);
                }
            }
            
            if (pendingItems.length > 0) {
                this.showNotification('Sync Complete', `${pendingItems.length} offline actions synced successfully.`, 'success');
            }
            
        } catch (error) {
            console.error('[PWA] Sync failed:', error);
        }
    }
    
    async syncItem(item) {
        const endpoint = this.getEndpointForType(item.type);
        
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(item.data)
        });
        
        if (!response.ok) {
            throw new Error(`Sync failed for ${item.type}`);
        }
        
        return response.json();
    }
    
    getEndpointForType(type) {
        const endpoints = {
            'registration': '/api/registrations',
            'payment': '/api/payments',
            'scoring': '/api/judging/scores',
            'general': '/api/offline-sync'
        };
        
        return endpoints[type] || endpoints.general;
    }
    
    // Enhanced Mobile Features
    initMobileFeatures() {
        this.initCameraFeatures();
        this.initQRScanner();
        this.initGestureSupport();
        this.initBiometricAuth();
        this.initWebShare();
    }
    
    // Camera Integration
    initCameraFeatures() {
        const cameraButtons = document.querySelectorAll('[data-camera-capture]');
        // Camera buttons found and initialized
        
        cameraButtons.forEach(button => {
            this.attachCameraListener(button);
        });

        // Watch for dynamically added camera buttons (like FAB buttons)
        this.watchForDynamicCameraButtons();

        // Also check for FAB buttons specifically after a delay
        setTimeout(() => {
            const fabButtons = document.querySelectorAll('.fab-action[data-camera-capture]');
            console.log('[PWA] Found', fabButtons.length, 'FAB camera buttons');
            fabButtons.forEach(button => {
                console.log('[PWA] FAB camera button:', button, 'visible:', button.offsetParent !== null);
                // Re-attach listeners to any FAB buttons that might have been missed
                if (!button.hasAttribute('data-camera-listener')) {
                    button.addEventListener('click', (e) => {
                        console.log('[PWA] FAB Camera button clicked, target:', button.dataset.cameraCapture);
                        e.preventDefault();
                        this.openCamera(button.dataset.cameraCapture);
                    });
                    button.setAttribute('data-camera-listener', 'true');
                }
            });
        }, 3000);
    }

    // Watch for dynamically added camera buttons
    watchForDynamicCameraButtons() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if the added node is a camera button
                        if (node.hasAttribute && node.hasAttribute('data-camera-capture')) {
                            console.log('[PWA] Dynamic camera button detected:', node);
                            this.attachCameraListener(node);
                        }

                        // Check if the added node contains camera buttons
                        const cameraButtons = node.querySelectorAll ? node.querySelectorAll('[data-camera-capture]') : [];
                        cameraButtons.forEach(button => {
                            console.log('[PWA] Dynamic camera button found in added content:', button);
                            this.attachCameraListener(button);
                        });
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Attach camera listener to a button
    attachCameraListener(button) {
        if (!button.hasAttribute('data-camera-listener')) {
            button.addEventListener('click', (e) => {
                console.log('[PWA] Dynamic camera button clicked, target:', button.dataset.cameraCapture);
                e.preventDefault();
                this.openCamera(button.dataset.cameraCapture);
            });
            button.setAttribute('data-camera-listener', 'true');
            console.log('[PWA] Camera listener attached to:', button);
        }
    }

    // Helper method to get site logo from database
    async getSiteLogo() {
        try {
            const response = await fetch('/api/getSiteLogo');
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.logo_path) {
                    return data.logo_path;
                }
            }
        } catch (error) {
            console.log('[PWA] Failed to load site logo from database, using fallback');
        }
        // Fallback logo
        return '/uploads/branding/logo_1751468505_rides_logo.png';
    }
    
    async openCamera(targetInput) {
        console.log('[PWA] openCamera called with targetInput:', targetInput);

        if (!('mediaDevices' in navigator) || !('getUserMedia' in navigator.mediaDevices)) {
            console.error('[PWA] Camera not supported');
            this.showNotification('Camera Not Available', 'Camera access is not supported on this device.', 'error');
            return;
        }

        try {
            console.log('[PWA] Requesting camera access...');
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment',
                    width: { ideal: 1920 },
                    height: { ideal: 1080 }
                }
            });

            console.log('[PWA] Camera access granted, showing modal...');
            await this.showCameraModal(stream, targetInput);

        } catch (error) {
            console.error('[PWA] Camera access failed:', error);
            this.showNotification('Camera Access Denied', 'Please allow camera access to take photos.', 'error');
        }
    }
    
    async showCameraModal(stream, targetInput) {
        console.log('[PWA] showCameraModal called');

        // Get site logo from database
        const siteLogo = await this.getSiteLogo();
        console.log('[PWA] Site logo loaded:', siteLogo);

        // Remove any existing backdrops first to prevent duplicates
        const existingBackdrops = document.querySelectorAll('.camera-modal-backdrop');
        existingBackdrops.forEach(bd => bd.remove());

        // Create backdrop to block page content
        const backdrop = document.createElement('div');
        backdrop.className = 'camera-modal-backdrop';
        backdrop.id = 'camera-backdrop';
        backdrop.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 1.0) !important;
            z-index: 999997 !important;
            pointer-events: none !important;
        `;
        document.body.appendChild(backdrop);
        console.log('[PWA] Camera backdrop created with ID:', backdrop.id);





        // Add body class to hide header
        document.body.classList.add('camera-modal-active');

        const modal = document.createElement('div');
        modal.className = 'camera-modal';
        modal.innerHTML = `
            <div class="camera-container">
                <div class="camera-banner" id="camera-banner-content">
                    <!-- Banner content will be loaded dynamically -->
                </div>
                <div class="camera-viewfinder">
                    <video id="camera-video" autoplay playsinline></video>
                    <canvas id="camera-canvas" style="display: none;"></canvas>
                </div>
                <div class="camera-controls">
                    <button class="btn btn-danger camera-cancel-btn">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button class="btn btn-primary camera-capture-btn" data-target="${targetInput}">
                        <i class="fas fa-camera"></i> Capture
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);



        const video = document.getElementById('camera-video');
        video.srcObject = stream;

        this.currentStream = stream;
        this.currentModal = modal;
        
        // Add event listeners for modal controls
        const cancelBtn = modal.querySelector('.camera-cancel-btn');
        const captureBtn = modal.querySelector('.camera-capture-btn');
        
        cancelBtn.addEventListener('click', () => {
            this.closeCamera();
        });
        
        captureBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Get entity data from the original button that triggered the camera
            const originalButton = document.querySelector(`[data-camera-capture="${targetInput}"]`);
            const entityType = originalButton ? originalButton.dataset.entityType : null;
            const entityId = originalButton ? originalButton.dataset.entityId : null;
            const csrfToken = originalButton ? originalButton.dataset.csrfToken : null;

            this.capturePhoto(targetInput, entityType, entityId, csrfToken);
        });
        
        // Add backdrop click to close
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeCamera();
            }
        });
        
        // Add escape key to close
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                this.closeCamera();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
        
        // Store escape handler for cleanup
        modal.escapeHandler = escapeHandler;
        
        // Force reload camera-banner script to bypass cache
        const forceReloadBannerScript = () => {
            return new Promise((resolve, reject) => {
                // Remove existing script if present
                const existingScript = document.querySelector('script[src*="camera-banner.js"]');
                if (existingScript) {
                    existingScript.remove();
                }
                
                // Load fresh script with timestamp
                const script = document.createElement('script');
                script.src = '/public/js/camera-banner.js?v=' + Date.now() + '&cache=bust';
                script.onload = () => {
                    console.log('Camera banner script reloaded successfully');
                    resolve();
                };
                script.onerror = () => {
                    console.error('Failed to reload camera banner script');
                    reject(new Error('Script reload failed'));
                };
                document.head.appendChild(script);
            });
        };

        // Initialize banner system
        const bannerContainer = document.getElementById('camera-banner-content');

        // Show fallback logo immediately while banners load
        const showFallbackLogo = () => {
            if (bannerContainer) {
                bannerContainer.innerHTML = `<img src="${siteLogo}" alt="Rowan Elite Rides" style="max-height: 100%; max-width: 100%; height: auto; width: auto; object-fit: contain;">`;
            }
        };

        // Show logo immediately
        showFallbackLogo();

        // Apply CSS fix for modal background that was covering banners
        const style = document.createElement('style');
        style.textContent = `
            .camera-modal {
                background: transparent !important;
            }
            .camera-banner {
                background: transparent !important;
                overflow: visible !important;
            }
        `;
        document.head.appendChild(style);

        // Initialize banner system
        setTimeout(() => {
            if (window.cameraBanner) {
                // Use existing banner system
                window.cameraBanner.startRotation('camera-banner-content');
            } else {
                // Load banner system
                forceReloadBannerScript().then(() => {
                    setTimeout(() => {
                        if (window.cameraBanner) {
                            window.cameraBanner.startRotation('camera-banner-content');
                        }
                    }, 500);
                }).catch((error) => {
                    // Fallback: Load banners directly from API
                    console.log('Banner script failed, using direct API fallback');
                    this.loadBannersDirectly(bannerContainer);
                });
            }
        }, 100);
    }

    loadBannersDirectly(bannerContainer) {
        fetch('/api/camera-banners.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.banners && data.banners.length > 0) {
                    let currentIndex = 0;
                    const showBanner = () => {
                        const banner = data.banners[currentIndex];
                        bannerContainer.innerHTML = `<img src="${banner.image_url}" alt="${banner.title}" class="logo-banner">`;
                        currentIndex = (currentIndex + 1) % data.banners.length;
                    };
                    showBanner();
                    setInterval(showBanner, 5000);
                }
            })
            .catch(error => {
                console.error('Failed to load banners:', error);
            });
    }

    capturePhoto(targetInput, entityType = null, entityId = null, csrfToken = null) {
        const video = document.getElementById('camera-video');
        const canvas = document.getElementById('camera-canvas');

        if (!video || !canvas) {
            console.error('Camera Error: Camera elements not found');
            return;
        }

        if (!video.videoWidth || !video.videoHeight) {
            console.error('Camera Error: Video not ready');
            return;
        }

        // Add visual feedback for capture
        const captureFlash = document.createElement('div');
        captureFlash.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 9999;
            opacity: 0;
            pointer-events: none;
        `;
        document.body.appendChild(captureFlash);

        // Flash effect
        captureFlash.style.opacity = '0.8';
        setTimeout(() => {
            captureFlash.style.opacity = '0';
            setTimeout(() => {
                if (captureFlash.parentNode) {
                    captureFlash.parentNode.removeChild(captureFlash);
                }
            }, 200);
        }, 100);

        const context = canvas.getContext('2d');

        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        context.drawImage(video, 0, 0);

        canvas.toBlob((blob) => {
            if (!blob) {
                console.error('Capture Error: Failed to create photo');
                this.showNotification('Capture Error', 'Failed to capture photo', 'error');
                return;
            }

            const file = new File([blob], 'camera-photo.jpg', { type: 'image/jpeg' });

            // Close camera immediately after capture for better UX
            this.closeCamera();

            // Handle the captured photo
            this.handleCapturedPhoto(file, targetInput, entityType, entityId, csrfToken);
        }, 'image/jpeg', 0.8);
    }
    
    handleCapturedPhoto(file, targetInput, entityType = null, entityId = null, csrfToken = null) {
        // If we have entity data, upload directly to image editor
        if (entityType && entityId) {
            // Show immediate feedback that photo was captured
            this.showNotification('Photo Captured', 'Photo captured! Starting upload...', 'success');

            // Start upload with progress
            this.uploadToImageEditor(file, entityType, entityId, csrfToken);
            return;
        }

        // Check if this is the default event photo capture (no specific target)
        if (targetInput === 'vehicle_image' && !entityType && !entityId) {
            this.handleEventPhotoCapture(file, csrfToken);
            return;
        }

        // Otherwise, handle as file input (existing behavior)
        const input = document.querySelector(`[name="${targetInput}"]`);
        if (input && input.type === 'file') {
            // Create a new FileList with the captured photo
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            input.files = dataTransfer.files;

            // Trigger change event
            input.dispatchEvent(new Event('change', { bubbles: true }));

            this.showNotification('Photo Captured', 'Photo captured successfully!', 'success');
        } else {
            console.error('Error: No entity data or file input found');
            this.showNotification('Capture Error', 'Unable to save captured photo', 'error');
        }
    }

    /**
     * Handle event photo capture from FAB menu
     */
    async handleEventPhotoCapture(file, csrfToken) {
        console.log('[PWA] Handling event photo capture');

        try {
            // First check if user can upload (admin bypass check)
            const canUpload = await this.checkCanUploadAtLocation(0, 0); // Use dummy coordinates for admin check

            // If admin bypass is enabled, skip location requirement
            if (canUpload.admin_bypass) {
                console.log('[PWA] Admin bypass detected, skipping location check');
                this.showNotification('Admin Access', 'All restrictions bypassed for administrator', 'info');
                this.showEventPhotoUploadModal(file, canUpload.event, { latitude: 0, longitude: 0 }, csrfToken);
                return;
            }

            // Get user's location for non-admin users
            const location = await this.getCurrentLocation();

            if (!location) {
                this.showEventPhotoLocationError();
                return;
            }

            // Check if user can upload at current location
            const locationCheck = await this.checkCanUploadAtLocation(location.latitude, location.longitude);

            if (locationCheck.can_upload) {
                // Check if this is admin bypass
                if (locationCheck.admin_bypass) {
                    let message = 'Administrator access granted';
                    if (locationCheck.location_bypass && locationCheck.time_bypass) {
                        message = 'All restrictions bypassed for administrator';
                    } else if (locationCheck.location_bypass) {
                        message = 'Location verification bypassed for administrator';
                    } else if (locationCheck.time_bypass) {
                        message = 'Time restrictions bypassed for administrator';
                    }
                    this.showNotification('Admin Access', message, 'info');
                } else if (locationCheck.gps_disabled) {
                    this.showNotification('GPS Disabled', 'Location verification is disabled', 'info');
                }

                // Show event photo upload modal
                this.showEventPhotoUploadModal(file, locationCheck.event, location, csrfToken);
            } else {
                // Show nearby events for manual check-in
                this.showNearbyEventsModal(file, location, csrfToken);
            }

        } catch (error) {
            console.error('[PWA] Error handling event photo capture:', error);
            this.showNotification('Error', 'Failed to process photo. Please try again.', 'error');
        }
    }

    /**
     * Get current location
     */
    getCurrentLocation() {
        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                reject(new Error('Geolocation not supported'));
                return;
            }

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    resolve({
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude
                    });
                },
                (error) => {
                    console.error('[PWA] Geolocation error:', error);
                    reject(error);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 300000 // 5 minutes
                }
            );
        });
    }

    /**
     * Check if user can upload at current location
     */
    async checkCanUploadAtLocation(latitude, longitude) {
        try {
            const formData = new FormData();
            formData.append('latitude', latitude);
            formData.append('longitude', longitude);

            const response = await fetch('/api/event-photos/can-upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            return result;

        } catch (error) {
            console.error('[PWA] Error checking upload permission:', error);
            return { success: false, can_upload: false };
        }
    }

    /**
     * Show location error and options
     */
    showEventPhotoLocationError() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="fas fa-map-marker-alt me-2"></i>Location Required
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>To share event photos, we need to verify you're at an event location.</p>
                        <p>Please enable location services and try again, or manually check in to an event.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="window.pwaFeatures.retryLocationCapture()">
                            <i class="fas fa-redo me-2"></i>Try Again
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    /**
     * Retry location capture
     */
    retryLocationCapture() {
        // Close any open modals
        const openModals = document.querySelectorAll('.modal.show');
        openModals.forEach(modal => {
            const bootstrapModal = bootstrap.Modal.getInstance(modal);
            if (bootstrapModal) {
                bootstrapModal.hide();
            }
        });

        // Trigger camera again
        setTimeout(() => {
            this.openCamera('vehicle_image');
        }, 500);
    }

    /**
     * Show event photo upload modal
     */
    showEventPhotoUploadModal(file, event, location, csrfToken) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-camera me-2"></i>Share Event Photo
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <div class="alert alert-success">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <strong>Event Detected:</strong> ${event.name}
                            </div>
                        </div>

                        <form id="eventPhotoUploadForm">
                            <div class="mb-3">
                                <label for="photoCategory" class="form-label">Photo Category</label>
                                <select class="form-select" id="photoCategory" name="category" required>
                                    <option value="vehicle">🚗 Vehicle Spotlight</option>
                                    <option value="atmosphere" selected>🎪 Event Atmosphere</option>
                                    <option value="awards">🏆 Awards & Judging</option>
                                    <option value="vendors">🍔 Food & Vendors</option>
                                    <option value="people">👥 People & Friends</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="photoCaption" class="form-label">Caption (Optional)</label>
                                <textarea class="form-control" id="photoCaption" name="caption" rows="3"
                                         placeholder="Share what's happening at this event..."></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="photoPrivacy" class="form-label">Privacy Level</label>
                                <select class="form-select" id="photoPrivacy" name="privacy_level">
                                    <option value="public" selected>🌍 Public - Everyone can see</option>
                                    <option value="attendees">👥 Event Attendees Only</option>
                                    <option value="friends">👫 Friends Only</option>
                                    <option value="private">🔒 Private - Only me</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-success" onclick="window.pwaFeatures.uploadEventPhotoToEditor()">
                            <i class="fas fa-upload me-2"></i>Share Photo
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Store data for upload
        this.eventPhotoData = {
            file: file,
            event: event,
            location: location,
            csrfToken: csrfToken
        };

        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
            this.eventPhotoData = null;
        });
    }

    /**
     * Show nearby events for manual check-in
     */
    async showNearbyEventsModal(file, location, csrfToken) {
        try {
            // Get nearby events
            const nearbyEvents = await this.getNearbyEvents(location.latitude, location.longitude);

            const modal = document.createElement('div');
            modal.className = 'modal fade';

            let eventsHtml = '';
            if (nearbyEvents.events && nearbyEvents.events.length > 0) {
                eventsHtml = nearbyEvents.events.map(event => `
                    <div class="card mb-2">
                        <div class="card-body p-3">
                            <h6 class="card-title mb-1">${event.name}</h6>
                            <p class="card-text small text-muted mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>${event.venue_name}
                                <br>
                                <i class="fas fa-calendar me-1"></i>${new Date(event.start_date).toLocaleDateString()}
                                <span class="ms-2">
                                    <i class="fas fa-route me-1"></i>${event.distance ? event.distance.toFixed(1) + ' miles' : 'Nearby'}
                                </span>
                            </p>
                            <button type="button" class="btn btn-sm btn-primary"
                                    onclick="window.pwaFeatures.checkInToEvent('${event.type}', ${event.id})">
                                <i class="fas fa-check me-1"></i>Check In
                            </button>
                        </div>
                    </div>
                `).join('');
            } else {
                eventsHtml = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No events found near your location. You may need to be closer to an event venue to share photos.
                    </div>
                `;
            }

            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-map-marker-alt me-2"></i>Nearby Events
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p class="mb-3">Select an event to check in and share your photo:</p>
                            <div style="max-height: 400px; overflow-y: auto;">
                                ${eventsHtml}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // Store data for later use
            this.eventPhotoData = {
                file: file,
                location: location,
                csrfToken: csrfToken
            };

            // Clean up when modal is hidden
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
                this.eventPhotoData = null;
            });

        } catch (error) {
            console.error('[PWA] Error showing nearby events:', error);
            this.showNotification('Error', 'Failed to load nearby events', 'error');
        }
    }

    /**
     * Get nearby events
     */
    async getNearbyEvents(latitude, longitude) {
        try {
            const formData = new FormData();
            formData.append('latitude', latitude);
            formData.append('longitude', longitude);

            const response = await fetch('/api/event-photos/nearby-events', {
                method: 'POST',
                body: formData
            });

            return await response.json();

        } catch (error) {
            console.error('[PWA] Error getting nearby events:', error);
            return { success: false, events: [] };
        }
    }

    /**
     * Upload event photo using existing image editor system
     */
    async uploadEventPhotoToEditor() {
        if (!this.eventPhotoData) {
            this.showNotification('Error', 'No photo data available', 'error');
            return;
        }

        try {
            // Get form data for event photo metadata
            const form = document.getElementById('eventPhotoUploadForm');
            const category = form.querySelector('#photoCategory').value;
            const caption = form.querySelector('#photoCaption').value;
            const privacyLevel = form.querySelector('#photoPrivacy').value;

            // Create entity ID for event photos (combine event type and ID)
            const entityId = `${this.eventPhotoData.event.type}_${this.eventPhotoData.event.id}`;

            // Show loading state
            const uploadBtn = document.querySelector('.btn-success');
            const originalText = uploadBtn.innerHTML;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
            uploadBtn.disabled = true;

            // Create FormData for image editor upload
            const formData = new FormData();
            formData.append('image', this.eventPhotoData.file);
            formData.append('csrf_token', this.eventPhotoData.csrfToken);
            formData.append('after_upload', 'browser');
            formData.append('pwa_camera_upload', '1');

            // Add event photo specific metadata
            formData.append('event_photo_category', category);
            formData.append('event_photo_caption', caption);
            formData.append('event_photo_privacy', privacyLevel);
            formData.append('event_photo_latitude', this.eventPhotoData.location.latitude);
            formData.append('event_photo_longitude', this.eventPhotoData.location.longitude);
            formData.append('event_photo_event_type', this.eventPhotoData.event.type);
            formData.append('event_photo_event_id', this.eventPhotoData.event.id);

            // Upload to existing image editor system
            const response = await fetch(`/image_editor/upload/event_photo/${entityId}`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Success', 'Photo shared successfully!', 'success');

                // Close modal
                const modal = document.querySelector('.modal.show');
                if (modal) {
                    const bootstrapModal = bootstrap.Modal.getInstance(modal);
                    if (bootstrapModal) {
                        bootstrapModal.hide();
                    }
                }

                // Redirect to image editor or gallery
                setTimeout(() => {
                    if (result.image && result.image.id) {
                        window.location.href = `/image_editor/edit/${result.image.id}`;
                    } else {
                        // Fallback to event page
                        if (this.eventPhotoData.event.type === 'show') {
                            window.location.href = `/show/view/${this.eventPhotoData.event.id}`;
                        } else {
                            window.location.href = `/calendar/event/${this.eventPhotoData.event.id}`;
                        }
                    }
                }, 1500);

            } else {
                this.showNotification('Error', result.message || 'Failed to upload photo', 'error');

                // Restore button
                uploadBtn.innerHTML = originalText;
                uploadBtn.disabled = false;
            }

        } catch (error) {
            console.error('[PWA] Error uploading event photo:', error);
            this.showNotification('Error', 'Failed to upload photo. Please try again.', 'error');

            // Restore button
            const uploadBtn = document.querySelector('.btn-success');
            if (uploadBtn) {
                uploadBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Share Photo';
                uploadBtn.disabled = false;
            }
        }
    }

    /**
     * Check in to an event
     */
    async checkInToEvent(eventType, eventId) {
        if (!this.eventPhotoData) {
            this.showNotification('Error', 'No location data available', 'error');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('event_type', eventType);
            formData.append('event_id', eventId);
            formData.append('latitude', this.eventPhotoData.location.latitude);
            formData.append('longitude', this.eventPhotoData.location.longitude);

            const response = await fetch('/api/event-photos/check-in', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Success', 'Checked in successfully!', 'success');

                // Close current modal
                const modal = document.querySelector('.modal.show');
                if (modal) {
                    const bootstrapModal = bootstrap.Modal.getInstance(modal);
                    if (bootstrapModal) {
                        bootstrapModal.hide();
                    }
                }

                // Show upload modal after brief delay
                setTimeout(() => {
                    const event = {
                        type: eventType,
                        id: eventId,
                        name: 'Selected Event' // We'll get the name from the check-in response if needed
                    };
                    this.showEventPhotoUploadModal(this.eventPhotoData.file, event, this.eventPhotoData.location);
                }, 500);

            } else {
                this.showNotification('Error', result.message || 'Failed to check in', 'error');
            }

        } catch (error) {
            console.error('[PWA] Error checking in to event:', error);
            this.showNotification('Error', 'Failed to check in. Please try again.', 'error');
        }
    }

    // Upload captured photo directly to image editor
    async uploadToImageEditor(file, entityType, entityId, csrfToken) {
        let progressContainer = null;

        try {
            // Create and show progress indicator
            progressContainer = this.createUploadProgress();
            this.updateUploadProgress(progressContainer, 0, 'Preparing upload...');

            const formData = new FormData();
            formData.append('image', file);
            formData.append('csrf_token', csrfToken);
            formData.append('after_upload', 'browser'); // Return to previous page after upload
            formData.append('pwa_camera_upload', '1'); // Flag to indicate this is a PWA camera upload

            // Create XMLHttpRequest for progress tracking
            const xhr = new XMLHttpRequest();

            // Track upload progress
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    this.updateUploadProgress(progressContainer, percentComplete, 'Uploading photo...');
                }
            });

            // Handle completion
            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    this.updateUploadProgress(progressContainer, 100, 'Upload complete! Opening editor...');

                    // Small delay to show completion, then follow the server redirect
                    setTimeout(() => {
                        this.removeUploadProgress(progressContainer);

                        // The server will automatically redirect to the image editor
                        // For PWA camera uploads, it will go directly to /image_editor/edit/{imageId}
                        const finalUrl = xhr.responseURL;
                        if (finalUrl && finalUrl !== window.location.href) {
                            window.location.href = finalUrl;
                        } else {
                            // Fallback if no redirect URL is available
                            window.location.href = `/image_editor/${entityType}/${entityId}`;
                        }
                    }, 1000);
                } else {
                    throw new Error(`HTTP ${xhr.status}: ${xhr.statusText}`);
                }
            });

            // Handle errors
            xhr.addEventListener('error', () => {
                throw new Error('Network error during upload');
            });

            // Start the upload
            xhr.open('POST', `/image_editor/upload/${entityType}/${entityId}`);
            xhr.send(formData);

        } catch (error) {
            console.error('Upload error:', error);
            if (progressContainer) {
                this.removeUploadProgress(progressContainer);
            }
            this.showNotification('Upload Failed', error.message, 'error');
        }
    }

    // Create upload progress indicator
    createUploadProgress() {
        // Remove any existing progress indicator
        const existing = document.getElementById('camera-upload-progress');
        if (existing) {
            existing.remove();
        }

        const progressContainer = document.createElement('div');
        progressContainer.id = 'camera-upload-progress';
        progressContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 10000;
            text-align: center;
            min-width: 250px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        `;

        progressContainer.innerHTML = `
            <div style="margin-bottom: 15px;">
                <i class="fas fa-camera" style="font-size: 24px; color: #007bff;"></i>
            </div>
            <div id="upload-status" style="margin-bottom: 10px; font-weight: bold;">Preparing upload...</div>
            <div style="background: #333; border-radius: 10px; overflow: hidden; margin-bottom: 10px;">
                <div id="upload-progress-bar" style="
                    height: 8px;
                    background: linear-gradient(90deg, #007bff, #0056b3);
                    width: 0%;
                    transition: width 0.3s ease;
                "></div>
            </div>
            <div id="upload-percentage" style="font-size: 14px; color: #ccc;">0%</div>
        `;

        document.body.appendChild(progressContainer);
        return progressContainer;
    }

    // Update upload progress
    updateUploadProgress(container, percentage, status) {
        if (!container) return;

        const progressBar = container.querySelector('#upload-progress-bar');
        const statusText = container.querySelector('#upload-status');
        const percentageText = container.querySelector('#upload-percentage');

        if (progressBar) {
            progressBar.style.width = `${Math.min(percentage, 100)}%`;
        }

        if (statusText) {
            statusText.textContent = status;
        }

        if (percentageText) {
            percentageText.textContent = `${Math.round(percentage)}%`;
        }
    }

    // Remove upload progress indicator
    removeUploadProgress(container) {
        if (container && container.parentNode) {
            container.style.opacity = '0';
            container.style.transform = 'translate(-50%, -50%) scale(0.9)';
            container.style.transition = 'all 0.3s ease';

            setTimeout(() => {
                if (container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            }, 300);
        }
    }
    
    closeCamera() {
        // Stop all camera tracks first
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => {
                track.stop();
                console.log('[PWA] Camera track stopped:', track.kind);
            });
            this.currentStream = null;
        }

        // Clear video source
        const video = document.getElementById('camera-video');
        if (video) {
            video.srcObject = null;
            video.src = '';
        }

        // Stop banner rotation
        if (window.cameraBanner) {
            window.cameraBanner.stopRotation();
        }

        // Remove modal completely
        if (this.currentModal) {
            // Remove escape key listener if it exists
            if (this.currentModal.escapeHandler) {
                document.removeEventListener('keydown', this.currentModal.escapeHandler);
            }
            
            this.currentModal.remove();
            console.log('[PWA] Camera modal removed');
            this.currentModal = null;
        }

        // Fallback cleanup for any remaining modal
        const modal = document.querySelector('.camera-modal');
        if (modal) {
            modal.remove();
            console.log('[PWA] Fallback camera modal cleanup');
        }

        // Remove all camera backdrops (more reliable than ID-based removal)
        const allBackdrops = document.querySelectorAll('.camera-modal-backdrop');
        if (allBackdrops.length > 0) {
            console.log(`[PWA] Removing ${allBackdrops.length} camera backdrop(s)`);
            allBackdrops.forEach(bd => bd.remove());
        }

        // Remove body class to show header again
        document.body.classList.remove('camera-modal-active');

        // Clear any remaining references
        this.currentStream = null;
    }
    
    // QR Code Scanner
    initQRScanner() {
        const qrButtons = document.querySelectorAll('[data-qr-scanner]');
        qrButtons.forEach(button => {
            button.addEventListener('click', () => this.openQRScanner());
        });
    }
    
    async openQRScanner() {
        // Initialize audio context (user interaction required for mobile)
        this.initializeAudio();

        if (!('BarcodeDetector' in window)) {
            // Fallback to library-based QR scanning
            await this.loadQRLibrary();
        }

        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: 'environment' }
            });

            await this.showQRScannerModal(stream);

        } catch (error) {
            console.error('[PWA] QR Scanner failed:', error);
            this.showNotification('QR Scanner Error', 'Unable to access camera for QR scanning.', 'error');
        }
    }
    
    async showQRScannerModal(stream) {
        // Get site logo from database
        const siteLogo = await this.getSiteLogo();

        // Remove any existing backdrops first to prevent duplicates
        const existingBackdrops = document.querySelectorAll('.camera-modal-backdrop');
        existingBackdrops.forEach(bd => bd.remove());

        // Create backdrop to block page content
        const backdrop = document.createElement('div');
        backdrop.className = 'camera-modal-backdrop';
        backdrop.id = 'qr-backdrop';
        backdrop.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 1.0) !important;
            z-index: 999997 !important;
            pointer-events: none !important;
        `;
        document.body.appendChild(backdrop);
        console.log('[PWA] QR backdrop created with ID:', backdrop.id);



        // Add body class to hide header
        document.body.classList.add('qr-scanner-active');

        const modal = document.createElement('div');
        modal.className = 'qr-scanner-modal';
        modal.innerHTML = `
            <div class="qr-scanner-container">


                <div class="qr-banner" id="qr-banner-content">
                    <!-- Banner content will be loaded dynamically -->
                </div>
                <div class="qr-viewfinder">
                    <video id="qr-video" autoplay playsinline></video>
                    <div class="qr-overlay">
                        <div class="qr-target"></div>
                    </div>
                </div>
                <div class="qr-controls">
                    <button class="btn btn-danger qr-cancel-btn">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <div class="qr-instructions">
                        Position QR code within the frame
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);



        const video = document.getElementById('qr-video');
        video.srcObject = stream;
        
        // Add event listeners for modal controls
        const cancelBtn = modal.querySelector('.qr-cancel-btn');
        
        cancelBtn.addEventListener('click', () => {
            this.closeQRScanner();
        });
        
        // Add backdrop click to close
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeQRScanner();
            }
        });
        
        // Add escape key to close
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                this.closeQRScanner();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
        
        // Store escape handler for cleanup
        modal.escapeHandler = escapeHandler;
        
        // Force reload camera-banner script to bypass cache
        const forceReloadBannerScript = () => {
            return new Promise((resolve, reject) => {
                // Remove existing script if present
                const existingScript = document.querySelector('script[src*="camera-banner.js"]');
                if (existingScript) {
                    existingScript.remove();
                }
                
                // Load fresh script with timestamp
                const script = document.createElement('script');
                script.src = '/public/js/camera-banner.js?v=' + Date.now() + '&cache=bust';
                script.onload = () => {
                    console.log('QR Camera banner script reloaded successfully');
                    resolve();
                };
                script.onerror = () => {
                    console.error('Failed to reload QR camera banner script');
                    reject(new Error('Script reload failed'));
                };
                document.head.appendChild(script);
            });
        };

        // Initialize banner system immediately - no delays
        const bannerContainer = document.getElementById('qr-banner-content');
        
        // Show fallback logo immediately while banners load
        const showFallbackLogo = () => {
            if (bannerContainer) {
                bannerContainer.innerHTML = `<img src="${siteLogo}" alt="Rowan Elite Rides" style="max-height: 100%; max-width: 100%; height: auto; width: auto; object-fit: contain;">`;
            }
        };
        
        // Show logo immediately
        showFallbackLogo();
        
        // Apply CSS fix for modal background that was covering banners
        const style = document.createElement('style');
        style.textContent = `
            .qr-scanner-modal {
                background: transparent !important;
            }
            .qr-banner {
                background: transparent !important;
                overflow: visible !important;
            }
        `;
        document.head.appendChild(style);
        
        // Initialize banner system
        setTimeout(() => {
            if (window.cameraBanner) {
                // Use existing banner system
                window.cameraBanner.startRotation('qr-banner-content');
            } else {
                // Load banner system
                forceReloadBannerScript().then(() => {
                    setTimeout(() => {
                        if (window.cameraBanner) {
                            window.cameraBanner.startRotation('qr-banner-content');
                        }
                    }, 500);
                }).catch(() => {
                    // Fallback: Load banners directly from API
                    this.loadBannersDirectly(bannerContainer);
                });
            }
        }, 100);

        this.currentStream = stream;
        this.currentModal = modal;
        this.startQRDetection(video);
    }
    
    loadBannersDirectly(bannerContainer) {
        fetch('/api/camera-banners.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.banners && data.banners.length > 0) {
                    let currentIndex = 0;
                    const showBanner = () => {
                        const banner = data.banners[currentIndex];
                        bannerContainer.innerHTML = `<img src="${banner.image_url}" alt="${banner.title}" class="logo-banner">`;
                        currentIndex = (currentIndex + 1) % data.banners.length;
                    };
                    showBanner();
                    setInterval(showBanner, 5000);
                }
            })
            .catch(error => {
                console.error('Failed to load banners:', error);
            });
    }
    
    async startQRDetection(video) {
        if ('BarcodeDetector' in window) {
            const barcodeDetector = new BarcodeDetector({ formats: ['qr_code'] });
            
            const detectQR = async () => {
                try {
                    const barcodes = await barcodeDetector.detect(video);
                    if (barcodes.length > 0) {
                        this.handleQRDetected(barcodes[0].rawValue);
                        return;
                    }
                } catch (error) {
                    console.error('[PWA] QR detection error:', error);
                }
                
                if (this.currentStream) {
                    requestAnimationFrame(detectQR);
                }
            };
            
            video.addEventListener('loadedmetadata', detectQR);
        } else {
            // Fallback QR detection using library
            this.fallbackQRDetection(video);
        }
    }
    
    handleQRDetected(qrData) {
        console.log('[PWA] QR Code detected:', qrData);

        // Play beep sound at max volume
        this.playQRBeepSound();

        // Add vibration feedback for additional confirmation
        if ('vibrate' in navigator) {
            navigator.vibrate([200, 100, 200]); // Two short vibrations
        }

        this.closeQRScanner();

        // Handle different QR code types
        if (qrData.includes('/vote/')) {
            window.location.href = qrData;
        } else if (qrData.includes('/registration/')) {
            window.location.href = qrData;
        } else if (qrData.includes('/show/')) {
            window.location.href = qrData;
        } else {
            this.showNotification('QR Code Scanned', `Detected: ${qrData}`, 'info');
        }
    }

    /**
     * Initialize audio context for QR beep sounds
     * Must be called after user interaction on mobile devices
     */
    initializeAudio() {
        if (this.audioInitialized) return;

        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

            // Resume audio context if suspended
            if (this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }

            this.audioInitialized = true;
            console.log('[PWA] Audio context initialized for QR beeps');
        } catch (error) {
            console.log('[PWA] Audio context initialization failed:', error);
        }
    }

    /**
     * Play QR code detection beep sound at maximum volume
     * Works even when phone volume is disabled/muted
     */
    playQRBeepSound() {
        try {
            // Initialize audio if not already done
            if (!this.audioInitialized) {
                this.initializeAudio();
            }

            // Use existing audio context or create new one
            const audioContext = this.audioContext || new (window.AudioContext || window.webkitAudioContext)();

            // Resume audio context if suspended (required for mobile)
            if (audioContext.state === 'suspended') {
                audioContext.resume();
            }

            // Create oscillator for beep sound
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            // Connect audio nodes
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // Configure beep sound - high frequency for attention
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime); // 1kHz tone
            oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.1);

            // Set maximum volume (1.0 = 100%)
            gainNode.gain.setValueAtTime(1.0, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            // Use square wave for more piercing sound
            oscillator.type = 'square';

            // Play beep for 200ms
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);

            console.log('[PWA] QR beep sound played');

            // Create a second shorter beep for confirmation
            setTimeout(() => {
                try {
                    const oscillator2 = audioContext.createOscillator();
                    const gainNode2 = audioContext.createGain();

                    oscillator2.connect(gainNode2);
                    gainNode2.connect(audioContext.destination);

                    oscillator2.frequency.setValueAtTime(1200, audioContext.currentTime);
                    oscillator2.type = 'square';

                    gainNode2.gain.setValueAtTime(1.0, audioContext.currentTime);
                    gainNode2.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

                    oscillator2.start(audioContext.currentTime);
                    oscillator2.stop(audioContext.currentTime + 0.1);
                } catch (e) {
                    console.log('[PWA] Second beep failed:', e);
                }
            }, 300);

        } catch (error) {
            console.log('[PWA] QR beep sound failed:', error);

            // Fallback: Try to use HTML5 audio with data URI
            try {
                // Create a short beep using data URI
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.volume = 1.0; // Maximum volume
                audio.play().catch(e => console.log('[PWA] Fallback audio failed:', e));
            } catch (e) {
                console.log('[PWA] Fallback beep failed:', e);

                // Final fallback: Vibration if available
                if ('vibrate' in navigator) {
                    navigator.vibrate([200, 100, 200]);
                    console.log('[PWA] Using vibration as audio fallback');
                }
            }
        }
    }

    closeQRScanner() {
        // Stop all camera tracks first
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => {
                track.stop();
                console.log('[PWA] QR Scanner track stopped:', track.kind);
            });
            this.currentStream = null;
        }

        // Clear video source
        const video = document.getElementById('qr-video');
        if (video) {
            video.srcObject = null;
            video.src = '';
        }

        // Stop banner rotation
        if (window.cameraBanner) {
            window.cameraBanner.stopRotation();
        }

        // Remove modal completely
        if (this.currentModal) {
            // Remove escape key listener if it exists
            if (this.currentModal.escapeHandler) {
                document.removeEventListener('keydown', this.currentModal.escapeHandler);
            }
            
            this.currentModal.remove();
            console.log('[PWA] QR Scanner modal removed');
            this.currentModal = null;
        }

        // Fallback cleanup for any remaining modal
        const modal = document.querySelector('.qr-scanner-modal');
        if (modal) {
            modal.remove();
            console.log('[PWA] Fallback QR scanner modal cleanup');
        }

        // Remove all camera backdrops (more reliable than ID-based removal)
        const allBackdrops = document.querySelectorAll('.camera-modal-backdrop');
        if (allBackdrops.length > 0) {
            console.log(`[PWA] Removing ${allBackdrops.length} QR backdrop(s)`);
            allBackdrops.forEach(bd => bd.remove());
        }

        // Remove body class to show header again
        document.body.classList.remove('qr-scanner-active');

        // Clear any remaining references
        this.currentStream = null;
    }
    
    // Force close any open camera/QR modals - safety method
    forceCloseAllModals() {
        console.log('[PWA] Force closing all camera/QR modals');
        
        // Stop any active streams
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => {
                track.stop();
                console.log('[PWA] Force stopped track:', track.kind);
            });
            this.currentStream = null;
        }
        
        // Remove camera modal
        const cameraModal = document.querySelector('.camera-modal');
        if (cameraModal) {
            if (cameraModal.escapeHandler) {
                document.removeEventListener('keydown', cameraModal.escapeHandler);
            }
            cameraModal.remove();
            console.log('[PWA] Force removed camera modal');
        }
        
        // Remove QR scanner modal
        const qrModal = document.querySelector('.qr-scanner-modal');
        if (qrModal) {
            if (qrModal.escapeHandler) {
                document.removeEventListener('keydown', qrModal.escapeHandler);
            }
            qrModal.remove();
            console.log('[PWA] Force removed QR scanner modal');
        }
        
        // Clear video sources
        const cameraVideo = document.getElementById('camera-video');
        const qrVideo = document.getElementById('qr-video');
        
        if (cameraVideo) {
            cameraVideo.srcObject = null;
            cameraVideo.src = '';
        }
        
        if (qrVideo) {
            qrVideo.srcObject = null;
            qrVideo.src = '';
        }
        
        // Stop banner rotation
        if (window.cameraBanner) {
            window.cameraBanner.stopRotation();
        }
        
        // Remove body classes to show header again
        document.body.classList.remove('camera-modal-active', 'qr-scanner-active');

        // Clear references
        this.currentStream = null;
        this.currentModal = null;
    }
    
    async loadQRLibrary() {
        // Load QR code library if needed
        if (!window.QrScanner) {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/qr-scanner@1.4.2/qr-scanner.min.js';
            document.head.appendChild(script);
            
            return new Promise((resolve) => {
                script.onload = resolve;
            });
        }
    }
    
    // Gesture Support
    initGestureSupport() {
        let startX, startY, currentX, currentY;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchmove', (e) => {
            currentX = e.touches[0].clientX;
            currentY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', () => {
            if (!startX || !startY || !currentX || !currentY) return;
            
            const diffX = startX - currentX;
            const diffY = startY - currentY;
            
            // Swipe detection
            if (Math.abs(diffX) > Math.abs(diffY)) {
                if (Math.abs(diffX) > 50) {
                    if (diffX > 0) {
                        this.handleSwipeLeft();
                    } else {
                        this.handleSwipeRight();
                    }
                }
            }
            
            // Reset
            startX = startY = currentX = currentY = null;
        });
    }
    
    handleSwipeLeft() {
        // Navigate to next page/section
        const nextButton = document.querySelector('[data-next]');
        if (nextButton) {
            nextButton.click();
        }
    }
    
    handleSwipeRight() {
        // Navigate to previous page/section
        const prevButton = document.querySelector('[data-prev]');
        if (prevButton) {
            prevButton.click();
        }
    }
    
    // Biometric Authentication
    async initBiometricAuth() {
        if ('credentials' in navigator && 'create' in navigator.credentials) {
            const biometricButton = document.getElementById('biometric-login');
            if (biometricButton) {
                biometricButton.addEventListener('click', () => this.authenticateWithBiometrics());
            }
        }
    }
    
    async authenticateWithBiometrics() {
        try {
            const credential = await navigator.credentials.get({
                publicKey: {
                    challenge: new Uint8Array(32),
                    allowCredentials: [],
                    userVerification: 'required'
                }
            });
            
            // Send credential to server for verification
            const response = await fetch('/api/auth/biometric', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ credential })
            });
            
            if (response.ok) {
                this.showNotification('Login Successful', 'Authenticated with biometrics!', 'success');
                // Enable push notifications after successful biometric login
                if (typeof enablePushNotifications === "function") {
                    enablePushNotifications();
                }
                window.location.reload();
            }
            
        } catch (error) {
            console.error('[PWA] Biometric authentication failed:', error);
            this.showNotification('Authentication Failed', 'Biometric authentication failed. Please try again.', 'error');
        }
    }
    
    // Web Share API
    initWebShare() {
        const shareButtons = document.querySelectorAll('[data-share]');
        shareButtons.forEach(button => {
            if (navigator.share) {
                button.addEventListener('click', () => this.shareContent(button.dataset));
            } else {
                button.style.display = 'none';
            }
        });
    }
    
    async shareContent(data) {
        try {
            await navigator.share({
                title: data.title || document.title,
                text: data.text || 'Check out this event!',
                url: data.url || window.location.href
            });
            
            console.log('[PWA] Content shared successfully');
            
        } catch (error) {
            console.error('[PWA] Share failed:', error);
        }
    }
    
    // Utility Methods
    showNotification(title, message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-header">
                <strong>${title}</strong>
                <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
            <div class="toast-body">${message}</div>
        `;
        
        // Add to toast container or create one
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(container);
        }
        
        container.appendChild(toast);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }
    
    // Show update banner when new service worker is available
    showUpdateBanner(forceShow = false) {
        const now = Date.now();
        
        if (!forceShow) {
            // Check if update was recently dismissed, applied, or shown
            const dismissed = sessionStorage.getItem('pwa_update_dismissed');
            const updateApplied = sessionStorage.getItem('pwa_update_applied');
            const lastShown = localStorage.getItem('pwa_last_update_shown');
            
            // Don't show if dismissed in this session, update was applied, or shown recently (within 30 minutes)
            if (dismissed === 'true' || 
                updateApplied === 'true' ||
                (lastShown && (now - parseInt(lastShown)) < 30 * 60 * 1000)) {
                console.log('[PWA] Update banner suppressed (recently dismissed, applied, or shown)');
                return;
            }
            
            // Check if there's actually a waiting service worker before showing banner
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration().then(registration => {
                    if (!registration || !registration.waiting) {
                        console.log('[PWA] No waiting service worker found, not showing banner');
                        return;
                    }
                    console.log('[PWA] Waiting service worker confirmed, showing banner');
                    this.actuallyShowBanner(now);
                }).catch(err => {
                    console.log('[PWA] Error checking service worker registration:', err);
                });
                return; // Exit here, actuallyShowBanner will be called if needed
            }
        } else {
            console.log('[PWA] Forcing update banner to show (cache clearing triggered)');
        }
        
        // For forced show or non-service worker environments
        this.actuallyShowBanner(now);
    }
    
    // Actually show the banner (separated from checks)
    actuallyShowBanner(now = Date.now()) {

        // Remove any existing update banners
        this.removeExistingUpdateBanners();

        console.log('[PWA] Showing update banner');
        
        const banner = document.createElement('div');
        banner.className = 'update-banner alert alert-info alert-dismissible fade show position-fixed';
        banner.style.cssText = `
            top: 20px !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            z-index: 9999 !important;
            max-width: 90% !important;
            width: 400px !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
            border: none !important;
            background: linear-gradient(135deg, #1338BE 0%, #0056b3 100%) !important;
            color: white !important;
            border-radius: 8px !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        `;
        
        banner.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-sync-alt me-2"></i>
                <div class="flex-grow-1">
                    <strong>App Update Available</strong>
                    <div class="small">A new version is ready to install</div>
                </div>
                <div class="ms-2">
                    <button type="button" class="btn btn-light btn-sm me-2" id="pwa-update-now">
                        <i class="fas fa-download me-1"></i>Update
                    </button>
                    <button type="button" class="btn-close btn-close-white" aria-label="Close" id="pwa-update-dismiss"></button>
                </div>
            </div>
        `;
        
        document.body.appendChild(banner);
        
        // Debug: Check if banner was actually added and is visible
        setTimeout(() => {
            const addedBanner = document.querySelector('.update-banner');
            if (addedBanner) {
                const computedStyle = window.getComputedStyle(addedBanner);
                console.log('[PWA] Banner added to DOM. Display:', computedStyle.display, 'Visibility:', computedStyle.visibility, 'Opacity:', computedStyle.opacity);
                console.log('[PWA] Banner position:', computedStyle.position, 'Top:', computedStyle.top, 'Left:', computedStyle.left);
                console.log('[PWA] Banner z-index:', computedStyle.zIndex);
            } else {
                console.log('[PWA] ERROR: Banner not found in DOM after creation');
            }
        }, 100);
        
        // Add event listeners
        document.getElementById('pwa-update-now').addEventListener('click', () => {
            this.applyUpdate();
        });
        
        document.getElementById('pwa-update-dismiss').addEventListener('click', () => {
            this.dismissUpdate();
        });
        
        // Auto-dismiss after 30 seconds if no action
        setTimeout(() => {
            if (document.querySelector('.update-banner')) {
                console.log('[PWA] Auto-dismissing update banner after 30 seconds');
                this.dismissUpdate();
            }
        }, 30000);
        
        // Store when we showed this banner
        localStorage.setItem('pwa_last_update_shown', now.toString());
    }
    
    // Apply the service worker update
    async applyUpdate() {
        console.log('[PWA] Apply update clicked');
        
        // Mark that update is being applied to prevent banner from reappearing
        sessionStorage.setItem('pwa_update_applied', 'true');
        sessionStorage.setItem('pwa_update_dismissed', 'true');
        localStorage.setItem('pwa_last_update_shown', Date.now().toString());
        
        // Show loading state
        const updateButton = document.getElementById('pwa-update-now');
        if (updateButton) {
            updateButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';
            updateButton.disabled = true;
        }
        
        // Remove the banner immediately
        const banner = document.querySelector('.update-banner');
        if (banner) {
            banner.remove();
        }
        
        try {
            if ('serviceWorker' in navigator) {
                const registration = await navigator.serviceWorker.getRegistration();
                console.log('[PWA] Service worker registration:', registration);
                console.log('[PWA] Waiting worker:', registration?.waiting);
                console.log('[PWA] Installing worker:', registration?.installing);
                console.log('[PWA] Active worker:', registration?.active);
                
                if (registration && registration.waiting) {
                    console.log('[PWA] Waiting service worker found, sending SKIP_WAITING message');
                    
                    // Set up controller change listener before sending message
                    const controllerChangePromise = new Promise((resolve) => {
                        navigator.serviceWorker.addEventListener('controllerchange', () => {
                            console.log('[PWA] Controller changed, reloading page');
                            resolve();
                        }, { once: true });
                    });
                    
                    // Tell the waiting service worker to skip waiting and become active
                    registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                    
                    // Wait for controller change or timeout
                    const timeoutPromise = new Promise((resolve) => {
                        setTimeout(() => {
                            console.log('[PWA] Timeout reached, forcing reload');
                            resolve();
                        }, 3000);
                    });
                    
                    await Promise.race([controllerChangePromise, timeoutPromise]);
                    window.location.reload();
                    
                } else {
                    console.log('[PWA] No waiting worker, clearing cache and reloading');
                    
                    // Clear caches to ensure fresh content
                    if ('caches' in window) {
                        const cacheNames = await caches.keys();
                        await Promise.all(cacheNames.map(name => caches.delete(name)));
                        console.log('[PWA] All caches cleared');
                    }
                    
                    // Small delay to show the loading state
                    setTimeout(() => {
                        window.location.reload(true); // Force reload from server
                    }, 1000);
                }
            } else {
                console.log('[PWA] Service worker not supported, reloading');
                setTimeout(() => {
                    window.location.reload(true);
                }, 1000);
            }
        } catch (error) {
            console.error('[PWA] Error applying update:', error);
            setTimeout(() => {
                window.location.reload(true); // Fallback to simple reload
            }, 1000);
        }
    }
    
    // Dismiss the update banner
    dismissUpdate() {
        console.log('[PWA] Dismiss update clicked');
        
        const banner = document.querySelector('.update-banner');
        if (banner) {
            banner.remove();
            console.log('[PWA] Update banner removed');
        }
        
        // Set flags to not show the banner again (longer suppression period)
        sessionStorage.setItem('pwa_update_dismissed', 'true');
        localStorage.setItem('pwa_last_update_shown', Date.now().toString());
        console.log('[PWA] Update dismissed for this session and next 30 minutes');
    }

    // Check for forced update after cache clearing
    async checkForcedUpdate() {
        try {
            // Check multiple sources for forced update flags
            const forceUpdate = localStorage.getItem('force_pwa_update_check');
            const cacheCleared = localStorage.getItem('pwa_cache_cleared');
            const showNotification = sessionStorage.getItem('show_pwa_update_notification');
            const forceUpdateAfterClear = localStorage.getItem('pwa_force_update_after_cache_clear');
            
            // Check cookie as backup
            const cookieForceUpdate = document.cookie.split(';').find(c => c.trim().startsWith('pwa_force_update='));
            
            console.log('[PWA] Checking forced update flags:', {
                forceUpdate,
                cacheCleared,
                showNotification,
                forceUpdateAfterClear,
                cookieForceUpdate: cookieForceUpdate ? 'found' : 'not found'
            });
            
            if (forceUpdate === 'true' || showNotification === 'true' || forceUpdateAfterClear === 'true' || cookieForceUpdate) {
                console.log('[PWA] Forced update check requested after cache clearing');
                
                // Clear the flags
                localStorage.removeItem('force_pwa_update_check');
                localStorage.removeItem('pwa_force_update_after_cache_clear');
                sessionStorage.removeItem('show_pwa_update_notification');
                
                // Clear cookie flag
                if (cookieForceUpdate) {
                    document.cookie = 'pwa_force_update=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/';
                }
                
                // Clear any dismissal flags to ensure banner shows
                sessionStorage.removeItem('pwa_update_dismissed');
                localStorage.removeItem('pwa_last_update_shown');
                
                // Force service worker update check
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        console.log('[PWA] Forcing service worker update check');
                        await registration.update();
                        
                        // Show update banner immediately (forced)
                        setTimeout(() => {
                            this.showUpdateBanner(true); // Pass true to force show
                        }, 1000);
                    } else {
                        // No service worker registration, but still show banner for cache refresh
                        console.log('[PWA] No service worker registration, showing banner anyway for cache refresh');
                        setTimeout(() => {
                            this.showUpdateBanner(true);
                        }, 1000);
                    }
                } else {
                    // Service worker not supported, but still show banner for manual refresh
                    console.log('[PWA] Service worker not supported, showing banner for manual refresh');
                    setTimeout(() => {
                        this.showUpdateBanner(true);
                    }, 1000);
                }
            }
            
            // Also check service worker cache for update flag
            if ('caches' in window) {
                try {
                    const cache = await caches.open('pwa-update-flags');
                    const response = await cache.match('/pwa-update-flag');
                    if (response) {
                        const data = await response.json();
                        if (data.updateRequired) {
                            console.log('[PWA] Service worker indicates update required');
                            
                            // Clear the flag
                            await cache.delete('/pwa-update-flag');
                            
                            // Show update banner (forced)
                            setTimeout(() => {
                                this.showUpdateBanner(true); // Pass true to force show
                            }, 1000);
                        }
                    }
                } catch (e) {
                    console.warn('[PWA] Could not check service worker update flag:', e);
                }
            }
            
        } catch (error) {
            console.warn('[PWA] Error checking for forced update:', error);
        }
    }

    // Simple check for update banner flag
    checkForUpdateBannerFlag() {
        console.log('[PWA] Checking for update banner flag...');
        
        const showBanner = sessionStorage.getItem('show_pwa_update_banner');
        console.log('[PWA] Update banner flag:', showBanner);
        
        if (showBanner === 'true') {
            console.log('[PWA] Showing update banner due to cache clearing');
            
            // Clear the flag
            sessionStorage.removeItem('show_pwa_update_banner');
            
            // Show the banner
            this.showUpdateBanner(true);
        } else {
            console.log('[PWA] No update banner flag found');
        }
    }

    // Remove any existing update banners from the page
    removeExistingUpdateBanners() {
        const existingBanners = document.querySelectorAll('.update-banner');
        existingBanners.forEach(banner => {
            banner.remove();
            console.log('[PWA] Removed existing update banner on initialization');
        });
    }

    // Apply the service worker update silently without showing banner
    async applyUpdateSilently() {
        console.log('[PWA] Applying update silently');

        // Remove any existing update banners first
        const existingBanner = document.querySelector('.update-banner');
        if (existingBanner) {
            existingBanner.remove();
            console.log('[PWA] Removed existing update banner');
        }

        // Set flag to indicate we're about to reload for an update
        sessionStorage.setItem('pwa_updating', 'true');

        try {
            if ('serviceWorker' in navigator) {
                const registration = await navigator.serviceWorker.getRegistration();

                if (registration && registration.waiting) {
                    console.log('[PWA] Waiting service worker found, sending SKIP_WAITING message');
                    // Tell the waiting service worker to skip waiting and become active
                    registration.waiting.postMessage({ type: 'SKIP_WAITING' });

                    // Listen for the controlling service worker to change
                    navigator.serviceWorker.addEventListener('controllerchange', () => {
                        console.log('[PWA] Controller changed, reloading page silently');
                        // Clear update flags since we're successfully updating
                        sessionStorage.removeItem('pwa_update_dismissed');
                        localStorage.removeItem('pwa_last_update_shown');
                        window.location.reload();
                    });

                    // Timeout fallback in case controllerchange doesn't fire
                    setTimeout(() => {
                        console.log('[PWA] Timeout reached, forcing reload');
                        window.location.reload();
                    }, 3000);
                } else {
                    console.log('[PWA] No waiting worker, reloading immediately');
                    // No waiting worker, just reload to get latest version
                    setTimeout(() => {
                        window.location.reload();
                    }, 500);
                }
            } else {
                console.log('[PWA] Service worker not supported, reloading');
                window.location.reload();
            }
        } catch (error) {
            console.error('[PWA] Error applying silent update:', error);
            window.location.reload(); // Fallback to simple reload
        }
    }

    // Clear authentication-related cache when login state changes
    async clearAuthCache() {
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            try {
                navigator.serviceWorker.controller.postMessage({
                    type: 'CLEAR_AUTH_CACHE'
                });
                console.log('[PWA] Sent cache clear message to service worker');
            } catch (error) {
                console.error('[PWA] Failed to clear auth cache:', error);
            }
        }
    }




}

// Make PWAFeatures available globally
window.PWAFeatures = PWAFeatures;

// Initialize PWA features when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Reset Facebook login flag on page load
    window.facebookLoginInProgress = false;
    if (!window.pwaFeatures) {
        window.pwaFeatures = new PWAFeatures();
    }
});

// Reset Facebook login flag when page becomes visible (user returns from Facebook)
document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
        window.facebookLoginInProgress = false;
    }
});

// Global function to clear auth cache (can be called from anywhere)
window.clearAuthCache = function() {
    if (window.pwaFeatures) {
        window.pwaFeatures.clearAuthCache();
    }
};

// Global function to force close camera modals (can be called from anywhere)
window.forceCloseCameraModals = function() {
    if (window.pwaFeatures) {
        window.pwaFeatures.forceCloseAllModals();
    }
};

// Global function to enable push notifications after login
window.enablePushNotifications = function() {
    if (window.pwaFeatures) {
        window.pwaFeatures.enablePushNotificationsAfterLogin();
    }
};

// Global function to handle Facebook login (can be called from anywhere)
window.handleFacebookLogin = function() {
    // Prevent multiple simultaneous calls
    if (window.facebookLoginInProgress) {
        console.log('[PWA] Facebook login already in progress, ignoring duplicate click');
        return;
    }

    console.log('[PWA] Starting Facebook login process');
    window.facebookLoginInProgress = true;

    const statusDiv = document.getElementById('facebook-login-status');
    const statusText = document.getElementById('facebook-status-text');
    const loginBtn = document.getElementById('facebook-login-btn');

    // IMMEDIATELY disable button and show feedback to prevent double-clicks
    if (loginBtn) {
        loginBtn.disabled = true;
        loginBtn.style.opacity = '0.6';
        loginBtn.style.cursor = 'not-allowed';
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Connecting to Facebook...';
    }

    // Show loading status
    if (statusDiv) {
        statusDiv.style.display = 'block';
        statusDiv.style.color = '#007bff';
    }
    if (statusText) {
        statusText.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Please wait, connecting to Facebook...';
    }

    // Use direct redirect for all cases - simpler and more reliable
    const baseUrl = window.PWA_CONFIG ? window.PWA_CONFIG.baseUrl : '';

    // Clear the flag after a timeout as a safety measure
    setTimeout(() => {
        window.facebookLoginInProgress = false;
    }, 15000); // 15 seconds timeout

    // Immediate redirect (no delay needed since we have visual feedback)
    console.log('[PWA] Redirecting to Facebook login');
    window.location.href = baseUrl + '/auth/facebook';
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PWAFeatures;
}

} // End duplicate loading prevention