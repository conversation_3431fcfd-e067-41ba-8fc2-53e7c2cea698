<?php
/**
 * Test Calendar Map Directions Feature
 * 
 * This test verifies that the calendar map directions functionality
 * has been properly implemented.
 */

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

require_once APPROOT . '/config/config.php';

echo "<h2>Calendar Map Directions Feature Test</h2>";

// Test 1: Check if CalendarController has user address functionality
echo "<h3>Test 1: CalendarController User Address Integration</h3>";
$calendarControllerPath = APPROOT . '/controllers/CalendarController.php';
if (file_exists($calendarControllerPath)) {
    $controllerContent = file_get_contents($calendarControllerPath);
    
    if (strpos($controllerContent, 'userAddress') !== false) {
        echo "✅ CalendarController contains userAddress functionality<br>";
    } else {
        echo "❌ CalendarController missing userAddress functionality<br>";
    }
    
    if (strpos($controllerContent, 'getUserById') !== false) {
        echo "✅ CalendarController uses getUserById method<br>";
    } else {
        echo "❌ CalendarController missing getUserById method<br>";
    }
} else {
    echo "❌ CalendarController.php not found<br>";
}

// Test 2: Check if map view has directions functionality
echo "<h3>Test 2: Map View Directions Integration</h3>";
$mapViewPath = APPROOT . '/views/calendar/map.php';
if (file_exists($mapViewPath)) {
    $mapContent = file_get_contents($mapViewPath);
    
    if (strpos($mapContent, 'getDirectionsToEvent') !== false) {
        echo "✅ Map view contains getDirectionsToEvent function<br>";
        
        // Check if function is defined globally (outside DOMContentLoaded)
        if (strpos($mapContent, 'function getDirectionsToEvent') !== false && 
            strpos($mapContent, 'document.addEventListener(\'DOMContentLoaded\'') !== false) {
            $globalFunctionPos = strpos($mapContent, 'function getDirectionsToEvent');
            $domContentLoadedPos = strpos($mapContent, 'document.addEventListener(\'DOMContentLoaded\'');
            
            if ($globalFunctionPos < $domContentLoadedPos) {
                echo "✅ getDirectionsToEvent function is defined globally<br>";
            } else {
                echo "❌ getDirectionsToEvent function is not in global scope<br>";
            }
        }
        
        // Check for duplicate function definitions
        $functionCount = substr_count($mapContent, 'function getDirectionsToEvent');
        if ($functionCount === 1) {
            echo "✅ Single getDirectionsToEvent function definition found<br>";
        } else {
            echo "❌ Multiple getDirectionsToEvent function definitions found ($functionCount)<br>";
        }
    } else {
        echo "❌ Map view missing getDirectionsToEvent function<br>";
    }
    
    if (strpos($mapContent, 'Get Directions') !== false) {
        echo "✅ Map view contains Get Directions button<br>";
    } else {
        echo "❌ Map view missing Get Directions button<br>";
    }
    
    if (strpos($mapContent, 'btn-success') !== false) {
        echo "✅ Map view has proper button styling<br>";
    } else {
        echo "❌ Map view missing button styling<br>";
    }
    
    if (strpos($mapContent, 'info-window') !== false) {
        echo "✅ Map view has info window styling<br>";
    } else {
        echo "❌ Map view missing info window styling<br>";
    }
} else {
    echo "❌ Map view file not found<br>";
}

// Test 3: Check version update
echo "<h3>Test 3: Version and Documentation Updates</h3>";
$configPath = APPROOT . '/config/config.php';
if (file_exists($configPath)) {
    $configContent = file_get_contents($configPath);
    
    if (strpos($configContent, '3.67.8') !== false) {
        echo "✅ Version updated to 3.67.8<br>";
    } else {
        echo "❌ Version not updated<br>";
    }
} else {
    echo "❌ Config file not found<br>";
}

$changelogPath = APPROOT . '/CHANGELOG.md';
if (file_exists($changelogPath)) {
    $changelogContent = file_get_contents($changelogPath);
    
    if (strpos($changelogContent, '3.67.8') !== false) {
        echo "✅ Changelog updated with new version<br>";
    } else {
        echo "❌ Changelog not updated<br>";
    }
    
    if (strpos($changelogContent, 'Get Directions') !== false) {
        echo "✅ Changelog contains Get Directions feature<br>";
    } else {
        echo "❌ Changelog missing Get Directions feature<br>";
    }
} else {
    echo "❌ Changelog file not found<br>";
}

$readmePath = APPROOT . '/README.md';
if (file_exists($readmePath)) {
    $readmeContent = file_get_contents($readmePath);
    
    if (strpos($readmeContent, '3.67.8') !== false) {
        echo "✅ README updated with new version<br>";
    } else {
        echo "❌ README not updated<br>";
    }
} else {
    echo "❌ README file not found<br>";
}

echo "<h3>Test Summary</h3>";
echo "<p>All tests completed. Check the results above to verify the implementation.</p>";

if (defined('DEBUG_MODE') && DEBUG_MODE) {
    echo "<h3>Debug Information</h3>";
    echo "<p>Debug mode is enabled.</p>";
    echo "<p>App version: " . (defined('APP_VERSION') ? APP_VERSION : 'Not defined') . "</p>";
}
?>