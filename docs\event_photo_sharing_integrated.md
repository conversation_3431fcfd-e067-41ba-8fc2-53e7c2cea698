# Event Photo Sharing System - Integrated Approach

**Version:** 2.0 (Integrated with existing image system)  
**Date:** January 29, 2025  
**Status:** Production Ready

## Overview

The Event Photo Sharing system allows users to capture and share photos from events and shows they're physically attending. The system integrates seamlessly with your existing image upload and management infrastructure.

## ✅ **CORRECTED APPROACH - Uses Your Existing Systems**

This system **integrates with your existing image editor** instead of creating a parallel system.

### Key Integration Points
- **Uses existing `images` table** with `entity_type = 'event_photo'`
- **Uses existing `ImageEditorController.php`** and `ImageEditorModel.php`
- **Uses existing PWA camera system** with enhanced event detection
- **Uses existing thumbnail generation** and image optimization
- **Only adds 1 new table** for event-specific metadata

## File Structure (Minimal)

### Modified Files (5)
```
public/js/pwa-features.js              # Enhanced FAB camera
controllers/ImageEditorController.php  # Added event photo support
models/ImageEditorModel.php           # Added metadata handling
views/show/view.php                   # Added "Event Photos" button
views/calendar/event.php              # Added "Event Photos" button
```

### New Files (2)
```
sql/event_photos_tables.sql          # Creates metadata table only
views/image_editor/event_gallery.php # Gallery view
```

## Database Changes (Minimal)

### Existing Tables (No Changes)
- `images` - Stores event photos with `entity_type = 'event_photo'`
- `users`, `events`, `shows` - Unchanged

### New Table (Only One)
```sql
event_photo_metadata
├── id (Primary Key)
├── image_id (Foreign Key → images.id)
├── category (vehicle, atmosphere, awards, vendors, people)
├── caption, privacy_level, latitude, longitude
├── event_type (event, show), event_id
└── timestamps
```

## User Flow

### Photo Capture
1. User clicks FAB camera → PWA detects location
2. If at event: Shows category/caption modal
3. Uploads via **existing image editor system**
4. Saves metadata to `event_photo_metadata` table
5. Redirects to **existing image editor** for further editing

### Gallery Access
1. User visits event/show page → Clicks "Event Photos"
2. Loads `ImageEditorController::eventGallery()`
3. Shows photos using **existing image system** + event metadata

## Installation

### 1. Database
```bash
mysql -u username -p database_name < sql/event_photos_tables.sql
```

### 2. Copy Files (7 total)
- 5 modified existing files
- 2 new files

### 3. Test
- Go to show/event page → Click "Event Photos"
- Test camera upload with location detection

## Key Features

### Location-Based Sharing
- GPS verification within 1 mile radius
- Manual check-in if GPS fails
- Time window: 24 hours before/after events

### Photo Categories
- 🚗 Vehicle Spotlight
- 🎪 Event Atmosphere  
- 🏆 Awards & Judging
- 🍔 Food & Vendors
- 👥 People & Friends

### Privacy Controls
- Public, Attendees Only, Friends Only, Private

### PWA Integration
- Enhanced FAB camera with event detection
- Uses existing camera upload system
- Mobile-first responsive design

## API Endpoints

```
GET  /image_editor/eventGallery/{event_type}/{event_id}
POST /image_editor/upload/event_photo/{entity_id}
```

## Benefits of Integrated Approach

✅ **Reuses existing infrastructure** - No duplicate code  
✅ **Minimal database changes** - Only 1 new table  
✅ **Consistent user experience** - Uses existing image editor  
✅ **Leverages existing features** - Thumbnails, optimization, management  
✅ **Future-proof** - Benefits from existing system improvements  

## Troubleshooting

**Photos not uploading:**
- Check GPS permissions and event radius
- Verify existing image upload settings work

**Gallery empty:**
- Ensure `event_photo_metadata` table exists
- Check entity ID format: `event_123` or `show_456`

**Location detection failing:**
- Enable browser location services
- Use manual check-in option

---

**This system integrates seamlessly with your existing architecture and requires minimal changes to your current setup.**
