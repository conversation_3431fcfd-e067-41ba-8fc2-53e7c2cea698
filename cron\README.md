# Event Photo System Cron Jobs

This directory contains automated maintenance scripts for the Event Photo Sharing System.

## Available Cron Jobs

### 1. Event Photo Cleanup (`event_photo_cleanup.php`)

**Purpose:** Automatically removes old event photos based on engagement levels and retention policies.

**Features:**
- Analyzes photo engagement (likes, comments, views)
- Categorizes photos as high, medium, or low engagement
- Removes photos that exceed their retention period
- Frees up disk space by deleting physical files
- Maintains database integrity
- Generates detailed cleanup reports

**Usage:**
```bash
# Normal cleanup (live mode)
php event_photo_cleanup.php

# Dry run (test mode - no actual deletions)
php event_photo_cleanup.php --dry-run

# Verbose output
php event_photo_cleanup.php --verbose

# Dry run with verbose output
php event_photo_cleanup.php --dry-run --verbose
```

**Recommended Schedule:**
```bash
# Run daily at 2:00 AM
0 2 * * * /usr/bin/php /path/to/cron/event_photo_cleanup.php
```

**Settings Controlled by Admin:**
- `auto_cleanup_enabled` - Enable/disable automatic cleanup
- `cleanup_frequency_days` - How often to run cleanup
- `high_engagement_threshold` - Minimum interactions for high engagement
- `medium_engagement_threshold` - Minimum interactions for medium engagement
- `high_engagement_retention_months` - Keep high engagement photos for X months
- `medium_engagement_retention_months` - Keep medium engagement photos for X months
- `low_engagement_retention_months` - Keep low engagement photos for X months

### 2. Event Photo Statistics (`event_photo_stats.php`)

**Purpose:** Generates daily, weekly, and monthly statistics for the event photo system.

**Features:**
- Calculates photo upload trends
- Tracks user engagement metrics
- Monitors storage usage
- Identifies popular photo categories
- Updates dashboard cache for fast loading
- Maintains historical statistics

**Usage:**
```bash
# Generate daily statistics
php event_photo_stats.php --daily

# Generate weekly statistics
php event_photo_stats.php --weekly

# Generate monthly statistics
php event_photo_stats.php --monthly

# Generate all statistics with verbose output
php event_photo_stats.php --daily --weekly --monthly --verbose
```

**Recommended Schedule:**
```bash
# Generate daily stats at 1:00 AM
0 1 * * * /usr/bin/php /path/to/cron/event_photo_stats.php --daily

# Generate weekly stats on Mondays at 1:30 AM
30 1 * * 1 /usr/bin/php /path/to/cron/event_photo_stats.php --weekly

# Generate monthly stats on the 1st of each month at 2:00 AM
0 2 1 * * /usr/bin/php /path/to/cron/event_photo_stats.php --monthly
```

## Installation Instructions

### 1. Set File Permissions
```bash
chmod +x /path/to/cron/event_photo_cleanup.php
chmod +x /path/to/cron/event_photo_stats.php
```

### 2. Create Log Directory
```bash
mkdir -p /path/to/logs
chmod 755 /path/to/logs
```

### 3. Test Scripts
```bash
# Test cleanup script in dry-run mode
php event_photo_cleanup.php --dry-run --verbose

# Test statistics generation
php event_photo_stats.php --daily --verbose
```

### 4. Add to Crontab
```bash
# Edit crontab
crontab -e

# Add these lines (adjust paths as needed):
0 1 * * * /usr/bin/php /path/to/cron/event_photo_stats.php --daily
30 1 * * 1 /usr/bin/php /path/to/cron/event_photo_stats.php --weekly
0 2 1 * * /usr/bin/php /path/to/cron/event_photo_stats.php --monthly
0 2 * * * /usr/bin/php /path/to/cron/event_photo_cleanup.php
```

## Database Tables Created

### Statistics Tables
- `event_photo_daily_stats` - Daily statistics
- `event_photo_weekly_stats` - Weekly statistics  
- `event_photo_monthly_stats` - Monthly statistics

### Settings Used
- `event_photo_settings` - All cleanup and retention settings
- `last_cleanup_run` - Timestamp of last cleanup execution

## Log Files

### Cleanup Logs
- **Location:** `/logs/event_photo_cleanup.log`
- **Content:** Cleanup operations, deletions, errors
- **Rotation:** Manual (consider logrotate)

### Statistics Logs
- **Location:** `/logs/event_photo_stats.log`
- **Content:** Statistics generation, calculations, errors
- **Rotation:** Manual (consider logrotate)

## Monitoring and Alerts

### Key Metrics to Monitor
- Cleanup success/failure rates
- Number of photos deleted per run
- Storage space freed
- Statistics generation success
- Error rates and types

### Recommended Alerts
- Email notification on cleanup failures
- Alert when storage usage exceeds thresholds
- Notification when statistics generation fails
- Warning when error rates increase

## Troubleshooting

### Common Issues

**1. Permission Denied**
```bash
# Fix file permissions
chmod +x /path/to/cron/*.php
chown www-data:www-data /path/to/cron/*.php
```

**2. Database Connection Errors**
- Verify database credentials in config.php
- Ensure database server is accessible from cron environment
- Check if database user has required permissions

**3. File Deletion Errors**
- Verify web server has write permissions to upload directories
- Check if files exist before attempting deletion
- Ensure sufficient disk space for operations

**4. Memory Issues**
- Increase PHP memory limit for large datasets
- Process photos in batches if needed
- Monitor memory usage during execution

### Debug Mode
```bash
# Run with maximum verbosity
php event_photo_cleanup.php --dry-run --verbose
php event_photo_stats.php --daily --verbose
```

## Security Considerations

- Scripts can only be run from command line (CLI)
- Database credentials should be secured
- Log files may contain sensitive information
- File deletion operations are irreversible
- Consider backup strategies before enabling cleanup

## Performance Notes

- Cleanup script processes all photos in memory
- Statistics generation queries can be intensive
- Consider running during low-traffic hours
- Monitor database performance during execution
- Large photo collections may require longer execution times

## Integration with Admin Panel

The cron jobs integrate with the admin settings:
- **Storage Settings:** Control cleanup behavior
- **Dashboard:** Display cached statistics
- **Logs:** View execution history and errors
- **Manual Triggers:** Run cleanup/stats on demand
