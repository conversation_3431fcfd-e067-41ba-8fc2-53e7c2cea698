<?php
/**
 * Event Photo Statistics Generator
 * 
 * This script generates daily/weekly/monthly statistics for event photo sharing
 * and updates cached statistics for the admin dashboard.
 * 
 * Usage: php event_photo_stats.php [--daily] [--weekly] [--monthly] [--verbose]
 * 
 * Schedule: Run daily via cron
 * Example crontab entry: 0 1 * * * /usr/bin/php /path/to/cron/event_photo_stats.php --daily
 */

// Prevent direct web access
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from the command line.');
}

// Set up paths
define('APPROOT', dirname(__DIR__));
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/libraries/Database.php';

class EventPhotoStats {
    private $db;
    private $verbose = false;
    private $generateDaily = false;
    private $generateWeekly = false;
    private $generateMonthly = false;

    public function __construct() {
        $this->db = new Database();
        $this->parseArguments();
    }

    /**
     * Parse command line arguments
     */
    private function parseArguments() {
        global $argv;
        
        if (isset($argv)) {
            foreach ($argv as $arg) {
                switch ($arg) {
                    case '--verbose':
                        $this->verbose = true;
                        break;
                    case '--daily':
                        $this->generateDaily = true;
                        break;
                    case '--weekly':
                        $this->generateWeekly = true;
                        break;
                    case '--monthly':
                        $this->generateMonthly = true;
                        break;
                }
            }
        }
        
        // If no specific period specified, generate daily stats
        if (!$this->generateDaily && !$this->generateWeekly && !$this->generateMonthly) {
            $this->generateDaily = true;
        }
    }

    /**
     * Main statistics generation
     */
    public function run() {
        $this->log("=== Event Photo Statistics Generator Started ===");
        
        try {
            // Create statistics tables if they don't exist
            $this->createStatsTables();
            
            if ($this->generateDaily) {
                $this->generateDailyStats();
            }
            
            if ($this->generateWeekly) {
                $this->generateWeeklyStats();
            }
            
            if ($this->generateMonthly) {
                $this->generateMonthlyStats();
            }
            
            // Update cached dashboard stats
            $this->updateDashboardCache();
            
        } catch (Exception $e) {
            $this->log("Error generating statistics: " . $e->getMessage(), true);
        }
        
        $this->log("=== Event Photo Statistics Generation Completed ===");
    }

    /**
     * Create statistics tables
     */
    private function createStatsTables() {
        try {
            // Daily statistics table
            $this->db->query('CREATE TABLE IF NOT EXISTS event_photo_daily_stats (
                id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
                stat_date DATE NOT NULL UNIQUE,
                total_photos INT DEFAULT 0,
                new_photos INT DEFAULT 0,
                total_users INT DEFAULT 0,
                active_users INT DEFAULT 0,
                total_events INT DEFAULT 0,
                active_events INT DEFAULT 0,
                total_storage_bytes BIGINT DEFAULT 0,
                avg_photos_per_event DECIMAL(10,2) DEFAULT 0,
                avg_photos_per_user DECIMAL(10,2) DEFAULT 0,
                top_category VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_stat_date (stat_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
            $this->db->execute();
            
            // Weekly statistics table
            $this->db->query('CREATE TABLE IF NOT EXISTS event_photo_weekly_stats (
                id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
                week_start DATE NOT NULL UNIQUE,
                week_end DATE NOT NULL,
                total_photos INT DEFAULT 0,
                new_photos INT DEFAULT 0,
                total_users INT DEFAULT 0,
                active_users INT DEFAULT 0,
                total_events INT DEFAULT 0,
                active_events INT DEFAULT 0,
                total_storage_bytes BIGINT DEFAULT 0,
                avg_photos_per_event DECIMAL(10,2) DEFAULT 0,
                avg_photos_per_user DECIMAL(10,2) DEFAULT 0,
                top_category VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_week_start (week_start)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
            $this->db->execute();
            
            // Monthly statistics table
            $this->db->query('CREATE TABLE IF NOT EXISTS event_photo_monthly_stats (
                id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
                month_year VARCHAR(7) NOT NULL UNIQUE,
                total_photos INT DEFAULT 0,
                new_photos INT DEFAULT 0,
                total_users INT DEFAULT 0,
                active_users INT DEFAULT 0,
                total_events INT DEFAULT 0,
                active_events INT DEFAULT 0,
                total_storage_bytes BIGINT DEFAULT 0,
                avg_photos_per_event DECIMAL(10,2) DEFAULT 0,
                avg_photos_per_user DECIMAL(10,2) DEFAULT 0,
                top_category VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_month_year (month_year)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
            $this->db->execute();
            
            $this->log("Statistics tables created/verified");
            
        } catch (Exception $e) {
            $this->log("Error creating statistics tables: " . $e->getMessage(), true);
            throw $e;
        }
    }

    /**
     * Generate daily statistics
     */
    private function generateDailyStats() {
        $this->log("Generating daily statistics...");
        
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $stats = $this->calculateStats($yesterday, $yesterday);
        
        try {
            $this->db->query('
                INSERT INTO event_photo_daily_stats 
                (stat_date, total_photos, new_photos, total_users, active_users, 
                 total_events, active_events, total_storage_bytes, avg_photos_per_event, 
                 avg_photos_per_user, top_category)
                VALUES 
                (:stat_date, :total_photos, :new_photos, :total_users, :active_users,
                 :total_events, :active_events, :total_storage_bytes, :avg_photos_per_event,
                 :avg_photos_per_user, :top_category)
                ON DUPLICATE KEY UPDATE
                total_photos = VALUES(total_photos),
                new_photos = VALUES(new_photos),
                total_users = VALUES(total_users),
                active_users = VALUES(active_users),
                total_events = VALUES(total_events),
                active_events = VALUES(active_events),
                total_storage_bytes = VALUES(total_storage_bytes),
                avg_photos_per_event = VALUES(avg_photos_per_event),
                avg_photos_per_user = VALUES(avg_photos_per_user),
                top_category = VALUES(top_category)
            ');
            
            $this->bindStatsParams($yesterday, $stats);
            $this->db->execute();
            
            $this->log("Daily statistics for {$yesterday} saved successfully");
            
        } catch (Exception $e) {
            $this->log("Error saving daily statistics: " . $e->getMessage(), true);
        }
    }

    /**
     * Generate weekly statistics
     */
    private function generateWeeklyStats() {
        $this->log("Generating weekly statistics...");
        
        $weekStart = date('Y-m-d', strtotime('last monday', strtotime('-1 week')));
        $weekEnd = date('Y-m-d', strtotime('sunday', strtotime($weekStart)));
        
        $stats = $this->calculateStats($weekStart, $weekEnd);
        
        try {
            $this->db->query('
                INSERT INTO event_photo_weekly_stats 
                (week_start, week_end, total_photos, new_photos, total_users, active_users, 
                 total_events, active_events, total_storage_bytes, avg_photos_per_event, 
                 avg_photos_per_user, top_category)
                VALUES 
                (:week_start, :week_end, :total_photos, :new_photos, :total_users, :active_users,
                 :total_events, :active_events, :total_storage_bytes, :avg_photos_per_event,
                 :avg_photos_per_user, :top_category)
                ON DUPLICATE KEY UPDATE
                week_end = VALUES(week_end),
                total_photos = VALUES(total_photos),
                new_photos = VALUES(new_photos),
                total_users = VALUES(total_users),
                active_users = VALUES(active_users),
                total_events = VALUES(total_events),
                active_events = VALUES(active_events),
                total_storage_bytes = VALUES(total_storage_bytes),
                avg_photos_per_event = VALUES(avg_photos_per_event),
                avg_photos_per_user = VALUES(avg_photos_per_user),
                top_category = VALUES(top_category)
            ');
            
            $this->db->bind(':week_start', $weekStart);
            $this->db->bind(':week_end', $weekEnd);
            $this->bindStatsParams($weekStart, $stats);
            $this->db->execute();
            
            $this->log("Weekly statistics for {$weekStart} to {$weekEnd} saved successfully");
            
        } catch (Exception $e) {
            $this->log("Error saving weekly statistics: " . $e->getMessage(), true);
        }
    }

    /**
     * Generate monthly statistics
     */
    private function generateMonthlyStats() {
        $this->log("Generating monthly statistics...");
        
        $monthYear = date('Y-m', strtotime('last month'));
        $monthStart = $monthYear . '-01';
        $monthEnd = date('Y-m-t', strtotime($monthStart));
        
        $stats = $this->calculateStats($monthStart, $monthEnd);
        
        try {
            $this->db->query('
                INSERT INTO event_photo_monthly_stats 
                (month_year, total_photos, new_photos, total_users, active_users, 
                 total_events, active_events, total_storage_bytes, avg_photos_per_event, 
                 avg_photos_per_user, top_category)
                VALUES 
                (:month_year, :total_photos, :new_photos, :total_users, :active_users,
                 :total_events, :active_events, :total_storage_bytes, :avg_photos_per_event,
                 :avg_photos_per_user, :top_category)
                ON DUPLICATE KEY UPDATE
                total_photos = VALUES(total_photos),
                new_photos = VALUES(new_photos),
                total_users = VALUES(total_users),
                active_users = VALUES(active_users),
                total_events = VALUES(total_events),
                active_events = VALUES(active_events),
                total_storage_bytes = VALUES(total_storage_bytes),
                avg_photos_per_event = VALUES(avg_photos_per_event),
                avg_photos_per_user = VALUES(avg_photos_per_user),
                top_category = VALUES(top_category)
            ');
            
            $this->db->bind(':month_year', $monthYear);
            $this->bindStatsParams($monthStart, $stats);
            $this->db->execute();
            
            $this->log("Monthly statistics for {$monthYear} saved successfully");
            
        } catch (Exception $e) {
            $this->log("Error saving monthly statistics: " . $e->getMessage(), true);
        }
    }

    /**
     * Calculate statistics for a date range
     */
    private function calculateStats($startDate, $endDate) {
        $stats = [];
        
        try {
            // Total photos up to end date
            $this->db->query('SELECT COUNT(*) as total FROM images WHERE entity_type = "event_photo" AND DATE(created_at) <= :end_date');
            $this->db->bind(':end_date', $endDate);
            $stats['total_photos'] = $this->db->single()->total ?? 0;
            
            // New photos in date range
            $this->db->query('SELECT COUNT(*) as new_photos FROM images WHERE entity_type = "event_photo" AND DATE(created_at) BETWEEN :start_date AND :end_date');
            $this->db->bind(':start_date', $startDate);
            $this->db->bind(':end_date', $endDate);
            $stats['new_photos'] = $this->db->single()->new_photos ?? 0;
            
            // Total storage used
            $this->db->query('SELECT SUM(file_size) as total_size FROM images WHERE entity_type = "event_photo" AND DATE(created_at) <= :end_date');
            $this->db->bind(':end_date', $endDate);
            $stats['total_storage_bytes'] = $this->db->single()->total_size ?? 0;
            
            // Active users (users who uploaded photos in period)
            $this->db->query('SELECT COUNT(DISTINCT user_id) as active_users FROM images WHERE entity_type = "event_photo" AND DATE(created_at) BETWEEN :start_date AND :end_date');
            $this->db->bind(':start_date', $startDate);
            $this->db->bind(':end_date', $endDate);
            $stats['active_users'] = $this->db->single()->active_users ?? 0;
            
            // Total users who have ever uploaded event photos
            $this->db->query('SELECT COUNT(DISTINCT user_id) as total_users FROM images WHERE entity_type = "event_photo" AND DATE(created_at) <= :end_date');
            $this->db->bind(':end_date', $endDate);
            $stats['total_users'] = $this->db->single()->total_users ?? 0;
            
            // Events with photos
            $this->db->query('SELECT COUNT(DISTINCT epm.event_id, epm.event_type) as active_events FROM event_photo_metadata epm JOIN images i ON epm.image_id = i.id WHERE DATE(i.created_at) BETWEEN :start_date AND :end_date');
            $this->db->bind(':start_date', $startDate);
            $this->db->bind(':end_date', $endDate);
            $stats['active_events'] = $this->db->single()->active_events ?? 0;
            
            // Total events with photos ever
            $this->db->query('SELECT COUNT(DISTINCT epm.event_id, epm.event_type) as total_events FROM event_photo_metadata epm JOIN images i ON epm.image_id = i.id WHERE DATE(i.created_at) <= :end_date');
            $this->db->bind(':end_date', $endDate);
            $stats['total_events'] = $this->db->single()->total_events ?? 0;
            
            // Calculate averages
            $stats['avg_photos_per_event'] = $stats['active_events'] > 0 ? round($stats['new_photos'] / $stats['active_events'], 2) : 0;
            $stats['avg_photos_per_user'] = $stats['active_users'] > 0 ? round($stats['new_photos'] / $stats['active_users'], 2) : 0;
            
            // Top category
            $this->db->query('SELECT epm.category, COUNT(*) as count FROM event_photo_metadata epm JOIN images i ON epm.image_id = i.id WHERE DATE(i.created_at) BETWEEN :start_date AND :end_date GROUP BY epm.category ORDER BY count DESC LIMIT 1');
            $this->db->bind(':start_date', $startDate);
            $this->db->bind(':end_date', $endDate);
            $topCategory = $this->db->single();
            $stats['top_category'] = $topCategory ? $topCategory->category : 'atmosphere';
            
        } catch (Exception $e) {
            $this->log("Error calculating statistics: " . $e->getMessage(), true);
            throw $e;
        }
        
        return $stats;
    }

    /**
     * Bind statistics parameters to prepared statement
     */
    private function bindStatsParams($date, $stats) {
        $this->db->bind(':stat_date', $date);
        $this->db->bind(':total_photos', $stats['total_photos']);
        $this->db->bind(':new_photos', $stats['new_photos']);
        $this->db->bind(':total_users', $stats['total_users']);
        $this->db->bind(':active_users', $stats['active_users']);
        $this->db->bind(':total_events', $stats['total_events']);
        $this->db->bind(':active_events', $stats['active_events']);
        $this->db->bind(':total_storage_bytes', $stats['total_storage_bytes']);
        $this->db->bind(':avg_photos_per_event', $stats['avg_photos_per_event']);
        $this->db->bind(':avg_photos_per_user', $stats['avg_photos_per_user']);
        $this->db->bind(':top_category', $stats['top_category']);
    }

    /**
     * Update cached dashboard statistics
     */
    private function updateDashboardCache() {
        $this->log("Updating dashboard cache...");
        
        try {
            $currentStats = $this->calculateStats(date('Y-m-d'), date('Y-m-d'));
            
            // Store in settings table for quick dashboard access
            $cacheData = [
                'dashboard_total_photos' => $currentStats['total_photos'],
                'dashboard_total_users' => $currentStats['total_users'],
                'dashboard_total_events' => $currentStats['total_events'],
                'dashboard_total_storage' => $currentStats['total_storage_bytes'],
                'dashboard_last_updated' => date('Y-m-d H:i:s')
            ];
            
            foreach ($cacheData as $key => $value) {
                $this->db->query('
                    INSERT INTO event_photo_settings (setting_name, setting_value) 
                    VALUES (:name, :value) 
                    ON DUPLICATE KEY UPDATE setting_value = :value
                ');
                $this->db->bind(':name', $key);
                $this->db->bind(':value', $value);
                $this->db->execute();
            }
            
            $this->log("Dashboard cache updated successfully");
            
        } catch (Exception $e) {
            $this->log("Error updating dashboard cache: " . $e->getMessage(), true);
        }
    }

    /**
     * Log message with timestamp
     */
    private function log($message, $isError = false) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}";
        
        if ($this->verbose || $isError) {
            echo $logMessage . "\n";
        }
        
        // Also log to file
        $logFile = APPROOT . '/logs/event_photo_stats.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logMessage . "\n", FILE_APPEND | LOCK_EX);
    }
}

// Run the statistics generation
try {
    $stats = new EventPhotoStats();
    $stats->run();
} catch (Exception $e) {
    echo "Fatal error: " . $e->getMessage() . "\n";
    exit(1);
}
