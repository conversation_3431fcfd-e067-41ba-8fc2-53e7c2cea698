<?php
/**
 * API Controller
 * 
 * This controller handles API requests and returns JSON responses
 */
class ApiController extends Controller {
    protected $db;
    
    /**
     * Initialize API controller
     * 
     * This method is called before any API endpoint method
     */
    private function initApi() {
        // Set content type to JSON for all methods in this controller
        header('Content-Type: application/json');
        
        // Prevent caching of API responses
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        header('Cache-Control: post-check=0, pre-check=0', false);
        header('Pragma: no-cache');
        
        // Initialize database connection
        $this->db = new Database();
    }
    
    /**
     * Default index method
     */
    public function index() {
        $this->initApi();
        $this->sendJsonResponse(['error' => 'No API endpoint specified']);
    }
    
    /**
     * Get site logo from system settings
     */
    public function getSiteLogo() {
        $this->initApi();
        
        try {
            // Get site logo from system settings
            $this->db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'site_logo'");
            $logo_result = $this->db->single();

            if ($logo_result && !empty($logo_result->setting_value)) {
                $logo_path = $logo_result->setting_value;
                
                $this->sendJsonResponse([
                    'success' => true,
                    'logo_path' => $logo_path,
                    'full_url' => BASE_URL . $logo_path
                ]);
            } else {
                // Fallback to default logo
                $this->sendJsonResponse([
                    'success' => true,
                    'logo_path' => '/uploads/branding/logo_1751468505_rides_logo.png',
                    'full_url' => BASE_URL . '/uploads/branding/logo_1751468505_rides_logo.png'
                ]);
            }
        } catch (Exception $e) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Failed to get site logo: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Get scoring settings for a specific show
     */
    public function getShowScoringSettings() {
        $this->initApi();
        
        // Disable error reporting for this method to prevent PHP errors from breaking JSON
        $previous_error_reporting = error_reporting(0);
        
        try {
            if ($_SERVER['REQUEST_METHOD'] != 'GET' || !isset($_GET['show_id'])) {
                $this->sendJsonResponse(['error' => 'Invalid request']);
                return;
            }
            
            $showId = (int)$_GET['show_id'];
            
            // Get scoring settings for this show
            $this->db->query("SELECT * FROM show_scoring_settings WHERE show_id = :show_id");
            $this->db->bind(':show_id', $showId);
            $settings = $this->db->single();
            
            // Log the settings for debugging
            error_log('API - Show ID: ' . $showId . ', Settings: ' . ($settings ? json_encode($settings) : 'none'));
            
            if ($settings) {
                // Make sure all required fields are present
                $responseSettings = [
                    'formula_id' => isset($settings->formula_id) ? (int)$settings->formula_id : null,
                    'weight_multiplier' => isset($settings->weight_multiplier) ? (float)$settings->weight_multiplier : 1.0,
                    'normalize_scores' => isset($settings->normalize_scores) ? (int)$settings->normalize_scores : 1
                ];
                
                $this->sendJsonResponse([
                    'success' => true,
                    'settings' => $responseSettings
                ]);
            } else {
                // No settings found, return default values
                $this->sendJsonResponse([
                    'success' => true,
                    'settings' => [
                        'formula_id' => null,
                        'weight_multiplier' => 1.0,
                        'normalize_scores' => 1
                    ]
                ]);
            }
        } catch (Exception $e) {
            error_log('API Error retrieving scoring settings: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Error retrieving settings: ' . $e->getMessage()
            ]);
        } finally {
            // Restore previous error reporting level
            error_reporting($previous_error_reporting);
        }
    }
    
    /**
     * Send a JSON response and exit
     * 
     * @param array $data Data to send as JSON
     */
    /**
     * Get camera banners for PWA modals
     * URL: /api/cameraBanners
     */
    public function cameraBanners() {
        $this->initApi();
        
        // Log API call for debugging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('[CAMERA_BANNERS_API] API endpoint called');
        }
        
        try {
            // Check if camera_banners table exists
            $this->db->query("SHOW TABLES LIKE 'camera_banners'");
            $this->db->execute();
            $table_exists = $this->db->rowCount() > 0;

            $banners = [];
            
            if ($table_exists) {
                // Get active banners
                $this->db->query("SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC");
                $this->db->execute();
                $results = $this->db->resultSet();
                
                foreach ($results as $row) {
                    $banner = [
                        'id' => (int)$row->id,
                        'type' => $row->type ?: 'text',
                        'text' => $row->text ?: '',
                        'image_path' => $row->image_path ?: '',
                        'alt_text' => $row->alt_text ?: '',
                        'active' => (bool)$row->active,
                        'sort_order' => (int)$row->sort_order
                    ];

                    // Explicitly set is_logo field
                    $banner['is_logo'] = false;
                    
                    // Ensure image path is absolute and not empty
                    if ($banner['image_path'] && !str_starts_with($banner['image_path'], 'http')) {
                        $banner['image_path'] = BASE_URL . '/uploads/banners/' . basename($banner['image_path']);
                    }
                    
                    $banners[] = $banner;
                }
            }

            // Get site logo from system settings
            $this->db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'site_logo'");
            $logo_result = $this->db->single();
            $site_logo = '';
            if ($logo_result && !empty($logo_result->setting_value)) {
                $site_logo = $logo_result->setting_value;
            }
            
            // Always add your logo as first banner (fixed position)
            $logo_banner = [
                'id' => -1, // Special ID for logo banner
                'type' => 'image',
                'text' => '',
                'image_path' => BASE_URL . $site_logo,
                'alt_text' => 'Rowan Elite Rides Logo',
                'active' => true,
                'sort_order' => -1, // Always first
                'is_logo' => true, // Special flag
                'duration' => 5000 // Always show for 5 seconds
            ];
            
            // Prepare final banner array
            $final_banners = [$logo_banner];
            
            // Add other banners (will be randomized in JavaScript)
            if (!empty($banners)) {
                $final_banners = array_merge($final_banners, $banners);
            }

            // Get rotation delay setting
            $delay = 5000; // Default 5 seconds
            try {
                $this->db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'camera_banner_delay' LIMIT 1");
                $this->db->execute();
                $delay_row = $this->db->single();
                
                if ($delay_row && !empty($delay_row->setting_value)) {
                    $delay = (int)$delay_row->setting_value;
                }
            } catch (Exception $e) {
                // Use default delay if setting doesn't exist
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('Camera banner delay setting not found, using default: ' . $e->getMessage());
                }
            }

            $response = [
                'success' => true,
                'banners' => $final_banners,
                'delay' => $delay,
                'count' => count($final_banners)
            ];

            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $response['debug'] = [
                    'table_exists' => $table_exists,
                    'banner_count' => count($final_banners),
                    'delay_setting' => $delay,
                    'logo_path' => $site_logo
                ];
                error_log('[CAMERA_BANNERS_API] Returning ' . count($final_banners) . ' banners');
            }

            $this->sendJsonResponse($response);

        } catch (PDOException $e) {
            $error_response = [
                'success' => false,
                'message' => 'Database error occurred'
            ];
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $error_response['debug'] = [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ];
            }
            
            http_response_code(500);
            $this->sendJsonResponse($error_response);

        } catch (Exception $e) {
            $error_response = [
                'success' => false,
                'message' => 'An error occurred'
            ];
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $error_response['debug'] = [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ];
            }
            
            http_response_code(500);
            $this->sendJsonResponse($error_response);
        }
    }

    /**
     * Get VAPID public key for FCM
     */
    public function getVapidKey() {
        $this->initApi();
        
        try {
            $vapidPublicKey = defined('VAPID_PUBLIC_KEY') ? VAPID_PUBLIC_KEY : null;
            
            if (empty($vapidPublicKey)) {
                throw new Exception('VAPID public key not configured');
            }
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('VAPID: Serving public key for push notifications');
            }
            
            $this->sendJsonResponse([
                'success' => true,
                'publicKey' => $vapidPublicKey
            ]);
            
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('VAPID Key API Error: ' . $e->getMessage());
            }
            
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Subscribe FCM token
     */
    public function fcmSubscribe() {
        $this->initApi();
        
        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            $this->sendJsonResponse(['success' => false, 'error' => 'User not logged in'], 401);
            return;
        }

        $userId = $_SESSION['user_id'];

        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);

        if (!isset($input['fcm_token'])) {
            $this->sendJsonResponse(['success' => false, 'error' => 'FCM token required'], 400);
            return;
        }

        $fcmToken = $input['fcm_token'];
        $userAgent = $input['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? '';

        try {
            // Check if token already exists for this user
            $this->db->query("
                SELECT id FROM fcm_tokens 
                WHERE user_id = :user_id AND token = :token AND is_active = 1
            ");
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':token', $fcmToken);
            
            if ($this->db->single()) {
                // Token already exists and is active
                $this->sendJsonResponse([
                    'success' => true,
                    'message' => 'Token already registered',
                    'token_id' => null
                ]);
                return;
            }

            // Deactivate old tokens for this user/device
            $this->db->query("
                UPDATE fcm_tokens 
                SET is_active = 0, updated_at = NOW() 
                WHERE user_id = :user_id AND user_agent = :user_agent
            ");
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':user_agent', $userAgent);
            $this->db->execute();

            // Insert new token
            $this->db->query("
                INSERT INTO fcm_tokens (user_id, token, user_agent, is_active, created_at, updated_at) 
                VALUES (:user_id, :token, :user_agent, 1, NOW(), NOW())
            ");
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':token', $fcmToken);
            $this->db->bind(':user_agent', $userAgent);
            
            if ($this->db->execute()) {
                $this->sendJsonResponse([
                    'success' => true,
                    'message' => 'FCM token registered successfully',
                    'token_id' => $this->db->lastInsertId()
                ]);
            } else {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Failed to register FCM token'
                ], 500);
            }

        } catch (Exception $e) {
            error_log("FCM Subscribe Error: " . $e->getMessage());
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Database error occurred'
            ], 500);
        }
    }

    /**
     * Unsubscribe FCM token
     */
    public function fcmUnsubscribe() {
        $this->initApi();
        
        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            $this->sendJsonResponse(['success' => false, 'error' => 'User not logged in'], 401);
            return;
        }

        $userId = $_SESSION['user_id'];

        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);

        if (!isset($input['fcm_token'])) {
            $this->sendJsonResponse(['success' => false, 'error' => 'FCM token required'], 400);
            return;
        }

        $fcmToken = $input['fcm_token'];

        try {
            // Deactivate the token
            $this->db->query("
                UPDATE fcm_tokens 
                SET is_active = 0, updated_at = NOW() 
                WHERE user_id = :user_id AND token = :token
            ");
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':token', $fcmToken);
            
            if ($this->db->execute()) {
                $this->sendJsonResponse([
                    'success' => true,
                    'message' => 'FCM token unsubscribed successfully'
                ]);
            } else {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Failed to unsubscribe FCM token'
                ], 500);
            }

        } catch (Exception $e) {
            error_log("FCM Unsubscribe Error: " . $e->getMessage());
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Database error occurred'
            ], 500);
        }
    }

    /**
     * Check if user can upload event photos at current location
     * Administrators bypass all location restrictions
     */
    public function canUploadEventPhoto() {
        $this->initApi();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendJsonResponse(['error' => 'Method not allowed'], 405);
            return;
        }

        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            $this->sendJsonResponse([
                'success' => false,
                'can_upload' => false,
                'message' => 'Authentication required'
            ], 401);
            return;
        }

        // ADMIN BYPASS: Administrators can upload from anywhere, anytime
        $auth = new Auth();
        if ($auth->hasRole('admin')) {
            // Find any event (active or not) for admin to upload to - no time restrictions
            $this->db->query("SELECT id, title, latitude, longitude, start_date, end_date
                             FROM calendar_events
                             ORDER BY start_date DESC
                             LIMIT 1");
            $event = $this->db->single();

            if ($event) {
                $this->sendJsonResponse([
                    'success' => true,
                    'can_upload' => true,
                    'admin_bypass' => true,
                    'time_bypass' => true,
                    'location_bypass' => true,
                    'message' => 'Administrator access - all restrictions bypassed',
                    'event' => [
                        'id' => $event->id,
                        'title' => $event->title,
                        'type' => 'event'
                    ]
                ]);
            } else {
                // Also check shows for admin
                $this->db->query("SELECT id, name as title, latitude, longitude, start_date, end_date
                                 FROM shows
                                 ORDER BY start_date DESC
                                 LIMIT 1");
                $show = $this->db->single();

                if ($show) {
                    $this->sendJsonResponse([
                        'success' => true,
                        'can_upload' => true,
                        'admin_bypass' => true,
                        'time_bypass' => true,
                        'location_bypass' => true,
                        'message' => 'Administrator access - all restrictions bypassed',
                        'event' => [
                            'id' => $show->id,
                            'title' => $show->title,
                            'type' => 'show'
                        ]
                    ]);
                } else {
                    $this->sendJsonResponse([
                        'success' => true,
                        'can_upload' => false,
                        'message' => 'No events or shows available'
                    ]);
                }
            }
            return;
        }

        // Regular users: Check location requirements
        $latitude = $_POST['latitude'] ?? null;
        $longitude = $_POST['longitude'] ?? null;

        if (!$latitude || !$longitude) {
            $this->sendJsonResponse([
                'success' => false,
                'can_upload' => false,
                'message' => 'Location coordinates required'
            ]);
            return;
        }

        // Get event photo settings
        $this->db->query("SELECT setting_name, setting_value FROM event_photo_admin_settings");
        $settingsResults = $this->db->resultSet();

        $settings = [];
        foreach ($settingsResults as $setting) {
            $settings[$setting->setting_name] = $setting->setting_value;
        }

        // Check if GPS verification is required
        if (($settings['require_gps_verification'] ?? '1') === '0') {
            // GPS verification disabled - find any active event
            $this->db->query("SELECT id, title, latitude, longitude, start_date, end_date
                             FROM calendar_events
                             WHERE status = 'active'
                             ORDER BY start_date DESC
                             LIMIT 1");
            $event = $this->db->single();

            if ($event) {
                $this->sendJsonResponse([
                    'success' => true,
                    'can_upload' => true,
                    'gps_disabled' => true,
                    'message' => 'GPS verification disabled - upload allowed',
                    'event' => [
                        'id' => $event->id,
                        'title' => $event->title,
                        'type' => 'event'
                    ]
                ]);
            } else {
                $this->sendJsonResponse([
                    'success' => true,
                    'can_upload' => false,
                    'message' => 'No active events available'
                ]);
            }
            return;
        }

        // GPS verification enabled - check location and time window
        $radiusMiles = floatval($settings['location_radius_miles'] ?? 1.0);
        $timeBufferHours = intval($settings['time_buffer_hours_before'] ?? 24);
        $timeBufferAfterHours = intval($settings['time_buffer_hours_after'] ?? 24);

        // Calculate time window
        $currentTime = date('Y-m-d H:i:s');

        // Find events within radius and time window
        $this->db->query("SELECT id, title, latitude, longitude, start_date, end_date,
                         (3959 * acos(cos(radians(:lat)) * cos(radians(latitude)) *
                         cos(radians(longitude) - radians(:lng)) + sin(radians(:lat)) *
                         sin(radians(latitude)))) AS distance
                         FROM calendar_events
                         WHERE status = 'active'
                         AND latitude IS NOT NULL
                         AND longitude IS NOT NULL
                         AND (
                             (:current_time BETWEEN DATE_SUB(start_date, INTERVAL :hours_before HOUR)
                              AND DATE_ADD(end_date, INTERVAL :hours_after HOUR))
                         )
                         HAVING distance <= :radius
                         ORDER BY distance ASC
                         LIMIT 1");

        $this->db->bind(':lat', $latitude);
        $this->db->bind(':lng', $longitude);
        $this->db->bind(':radius', $radiusMiles);
        $this->db->bind(':current_time', $currentTime);
        $this->db->bind(':hours_before', $timeBufferHours);
        $this->db->bind(':hours_after', $timeBufferAfterHours);

        $nearbyEvent = $this->db->single();

        if ($nearbyEvent) {
            $this->sendJsonResponse([
                'success' => true,
                'can_upload' => true,
                'message' => 'Location verified - you are at an event',
                'event' => [
                    'id' => $nearbyEvent->id,
                    'title' => $nearbyEvent->title,
                    'type' => 'event',
                    'distance' => round($nearbyEvent->distance, 2)
                ]
            ]);
        } else {
            $this->sendJsonResponse([
                'success' => true,
                'can_upload' => false,
                'message' => 'You are not within ' . $radiusMiles . ' miles of any active event during the allowed time window'
            ]);
        }
    }

    /**
     * Send JSON response with proper HTTP status code
     */
    private function sendJsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}
