# Sprint 1 UX Testing Guide 📱

## **🎯 Testing Objectives**
Test all Sprint 1 components on mobile to ensure smooth, intuitive user experience.

---

## **📋 Component Testing Checklist**

### **1. Role Indicator Badges**
**Location**: Header (next to logo)

**✅ Test Steps:**
- [ ] Login as different user roles
- [ ] Check if colored badge appears in header
- [ ] Hover/tap badge - tooltip should show capabilities
- [ ] Badge should show: Guest, Member, Staff, Judge, Coordinator, Administrator

**🔍 Look For:**
- Badge visibility and colors
- Tooltip readability on mobile
- Consistent labeling (no "User" vs "Member" confusion)

---

### **2. Breadcrumb Navigation**
**Location**: Below header on most pages

**✅ Test Steps:**
- [ ] Navigate to: Dashboard, Admin pages, Show pages, Calendar
- [ ] Check breadcrumb appears: "Home > [Section] > [Page]"
- [ ] Tap breadcrumb links - should navigate correctly
- [ ] "Viewing as: [Role]" should show consistent role names

**🔍 Look For:**
- Breadcrumbs appear on most pages (not just a few)
- Clickable navigation works
- Mobile responsive (hidden on very small screens)
- Role consistency (Member, not User)

---

### **3. Dashboard Quick Actions**
**Location**: Top of user dashboard

**✅ Test Steps:**
- [ ] Go to `/user/dashboard`
- [ ] Check "⚡ Quick Actions" header is visible
- [ ] Check "Get things done faster" subtitle is visible
- [ ] See 4 action cards with colored icons
- [ ] If no vehicles: "Add Vehicle" should have red "Start Here!" badge
- [ ] Tap cards - should navigate to correct pages

**🔍 Look For:**
- Lightning bolt icon is RED and visible
- All text is readable (not hidden by Bootstrap)
- Cards have hover effects
- Icons have colored gradients
- "Start Here!" badge pulses if applicable

---

### **4. Bottom Navigation + More Menu**
**Location**: Bottom of screen on mobile

**✅ Test Steps:**
- [ ] Check bottom nav shows: Home, Shows, Events, Messages, More
- [ ] Tap "More" button (⋯) - should slide up menu
- [ ] Menu should show role-specific options
- [ ] Tap backdrop to close menu
- [ ] Tap X button to close menu

**🔍 Look For:**
- "More" button actually works (slides up menu)
- Menu content matches your role
- Smooth slide-up animation
- Easy to close (backdrop tap + X button)
- Menu items are clickable

---

### **5. Enhanced Racing Drawer**
**Location**: Tap "MENU" button in header

**✅ Test Steps:**
- [ ] Tap "MENU" button in header
- [ ] Drawer should slide in from left
- [ ] Check organized sections:
  - 🌐 Public Access (Events, Shows)
  - 👤 Member Area (Dashboard) - if logged in
  - 👑 Management - if admin/coordinator
  - 👤 Account (Logout/Login)
- [ ] Tap X button in header to close
- [ ] Tap overlay to close

**🔍 Look For:**
- Sections are visually separated
- Section headers are visible
- Buttons are larger and easier to tap
- Close button works
- Racing theme styling intact

---

### **6. Context-Aware FAB**
**Location**: Floating button (bottom right)

**✅ Test Steps:**
- [ ] **Show page**: FAB should be GREEN with car icon
  - Tap: Should show "Register" + calendar/share options
- [ ] **Vehicle page**: FAB should be BLUE with car icon  
  - Tap: Should show "Add Vehicle" + camera option
- [ ] **Dashboard**: FAB should be PURPLE with dashboard icon
  - Tap: Should show "Add Vehicle" + find shows
- [ ] **Admin page**: FAB should be RED with crown icon
  - Tap: Should show "Create Show" + manage users

**🔍 Look For:**
- FAB color changes based on page context
- Primary action has text label (not just icon)
- Actions are relevant to current page
- No duplicate actions between FAB and More menu

---

### **7. Progressive Registration**
**Location**: `/auth/register` or `/progressive_auth/register`

**✅ Test Steps:**
- [ ] Go to `/auth/register` - should redirect to progressive flow
- [ ] See 3-step progress indicator (● ○ ○)
- [ ] Step 1: Email field + "Continue" button + Facebook option
- [ ] Enter email, click Continue - should advance to Step 2
- [ ] Step 2: Name + Password fields
- [ ] Step 3: Location + Interests (if reached)

**🔍 Look For:**
- Beautiful 3-step design (not old form)
- Progress indicators work
- Social proof stats visible
- Facebook login prominent
- Mobile-friendly form fields

---

### **8. Guest Conversion Overlays**
**Location**: Triggered on guest actions

**✅ Test Steps:**
- [ ] **Logout first** to test as guest
- [ ] Go to any show page
- [ ] Tap "Register for this Show" button
- [ ] Should see popup overlay with conversion message
- [ ] Check Facebook + Email signup options
- [ ] Test "Continue Browsing" option

**🔍 Look For:**
- Overlay appears when guest tries to register
- Professional design with benefits list
- Multiple signup options
- Easy to dismiss if not interested

---

## **🐛 Issue Reporting Format**

When you find issues, report them like this:

**Component**: [Name]
**Issue**: [What's wrong]
**Expected**: [What should happen]
**Device**: [Phone model/browser]
**Screenshot**: [If possible]

**Example:**
- **Component**: Dashboard Quick Actions
- **Issue**: Lightning bolt icon not visible, text is gray
- **Expected**: Red lightning bolt, black text
- **Device**: iPhone Safari

---

## **🎯 Priority Issues to Watch For**

1. **Visibility Issues**: Text/icons not showing due to Bootstrap conflicts
2. **Touch Targets**: Buttons too small or hard to tap on mobile
3. **Navigation Confusion**: Inconsistent role names or broken links
4. **JavaScript Errors**: Buttons that don't work (More menu, FAB, etc.)
5. **Responsive Issues**: Components not adapting to mobile screens
6. **Performance**: Slow loading or laggy animations

---

## **✅ Success Criteria**

Sprint 1 is polished when:
- [ ] All components are visible and functional on mobile
- [ ] Navigation is intuitive and consistent
- [ ] No JavaScript errors or broken buttons
- [ ] Role-based features work correctly
- [ ] Mobile experience feels smooth and professional
- [ ] Users can complete common tasks easily

---

**Ready to test? Start with the Dashboard Quick Actions since that was having visibility issues, then work through the rest of the checklist!** 🚀
