<?php
/**
 * Facebook Crawler Helper
 * 
 * This helper detects Facebook's crawler and provides appropriate content
 * to prevent "Access Denied" errors when sharing links.
 */

/**
 * Check if the current request is from Facebook's crawler
 * 
 * @return bool True if request is from Facebook's crawler
 */
function isFacebookCrawler() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Facebook's crawler user agents
    $facebookBots = [
        'facebookexternalhit',
        'Facebot',
        'FacebookBot',
        'facebook',
        'WhatsApp'
    ];
    
    foreach ($facebookBots as $bot) {
        if (stripos($userAgent, $bot) !== false) {
            return true;
        }
    }
    
    return false;
}

/**
 * Get appropriate content for Facebook crawler
 * 
 * @param string $pageType Type of page (event, show, home, etc.)
 * @param array $data Page data
 * @return array Array with title, description, image, url
 */
function getFacebookCrawlerContent($pageType = 'home', $data = []) {
    $baseUrl = defined('BASE_URL') ? BASE_URL : 'https://events.rowaneliterides.com';
    $siteName = defined('APP_NAME') ? APP_NAME : 'Rowan Elite Rides';
    
    // Default content
    $content = [
        'title' => $siteName,
        'description' => 'Rowan Elite Rides - Public Events and Shows for Car Enthusiasts. Join us for exciting automotive events, car shows, and community gatherings.',
        'image' => $baseUrl . '/public/images/logo.png',
        'url' => $baseUrl,
        'type' => 'website'
    ];
    
    // Customize based on page type
    switch ($pageType) {
        case 'event':
            if (isset($data['event'])) {
                $event = $data['event'];
                $content['title'] = $event->title . ' - ' . $siteName;
                $content['description'] = 'Join us for ' . $event->title . ' on ' . date('F j, Y', strtotime($event->start_date)) . '. ' . ($event->description ?? 'An exciting automotive event.');
                $content['type'] = 'event';
                if (!empty($event->featured_image)) {
                    $content['image'] = $baseUrl . '/' . $event->featured_image;
                }
            }
            break;
            
        case 'show':
            if (isset($data['show'])) {
                $show = $data['show'];
                $content['title'] = $show->name . ' - ' . $siteName;
                $content['description'] = 'Join us for ' . $show->name . ' on ' . date('F j, Y', strtotime($show->start_date)) . '. ' . ($show->description ?? 'An exciting car show.');
                $content['type'] = 'event';
                if (!empty($show->featured_image)) {
                    $content['image'] = $baseUrl . '/' . $show->featured_image;
                }
            }
            break;
            
        case 'gallery':
            $content['title'] = 'Photo Gallery - ' . $siteName;
            $content['description'] = 'Check out photos from our latest automotive events and car shows.';
            break;
            
        case 'about':
            $content['title'] = 'About Us - ' . $siteName;
            $content['description'] = 'Learn more about Rowan Elite Rides and our passion for automotive events and community.';
            break;
    }
    
    return $content;
}

/**
 * Output Facebook-friendly HTML for crawlers
 * 
 * @param string $pageType Type of page
 * @param array $data Page data
 */
function outputFacebookCrawlerHTML($pageType = 'home', $data = []) {
    $content = getFacebookCrawlerContent($pageType, $data);
    $siteName = defined('APP_NAME') ? APP_NAME : 'Rowan Elite Rides';
    
    header('Content-Type: text/html; charset=UTF-8');
    
    echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($content['title']) . '</title>
    <meta name="description" content="' . htmlspecialchars($content['description']) . '">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="' . htmlspecialchars($siteName) . '">
    <meta property="og:title" content="' . htmlspecialchars($content['title']) . '">
    <meta property="og:description" content="' . htmlspecialchars($content['description']) . '">
    <meta property="og:url" content="' . htmlspecialchars($content['url']) . '">
    <meta property="og:type" content="' . htmlspecialchars($content['type']) . '">
    <meta property="og:image" content="' . htmlspecialchars($content['image']) . '">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="' . htmlspecialchars($content['title']) . '">
    <meta name="twitter:description" content="' . htmlspecialchars($content['description']) . '">
    <meta name="twitter:image" content="' . htmlspecialchars($content['image']) . '">
</head>
<body>
    <h1>' . htmlspecialchars($content['title']) . '</h1>
    <p>' . htmlspecialchars($content['description']) . '</p>
    <p><a href="' . htmlspecialchars($content['url']) . '">Visit ' . htmlspecialchars($siteName) . '</a></p>
</body>
</html>';
    exit;
}

/**
 * Handle Facebook crawler request
 * 
 * Call this function early in your page processing to handle Facebook crawlers
 * 
 * @param string $pageType Type of page
 * @param array $data Page data
 */
function handleFacebookCrawler($pageType = 'home', $data = []) {
    if (isFacebookCrawler()) {
        outputFacebookCrawlerHTML($pageType, $data);
    }
}
