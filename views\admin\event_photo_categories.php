<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-tags me-2"></i><?php echo $data['title']; ?></h2>
                    <p class="text-muted">Customize the category labels and icons displayed on event photos</p>
                </div>
                <a href="<?php echo BASE_URL; ?>/admin/settings_event_photos" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Event Photos
                </a>
            </div>

            <!-- Flash Messages -->
            <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="alert alert-<?php echo $_SESSION['flash_message']['type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['flash_message']['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['flash_message']); ?>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Category Labels & Icons</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo BASE_URL; ?>/admin/event_photo_admin_categories">
                        <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_vehicle" class="form-label">Vehicle Category</label>
                                    <div class="input-group">
                                        <select class="form-select" style="max-width: 80px;" id="vehicle_icon" name="vehicle_icon">
                                            <?php
                                            $vehicleIcons = ['🚗', '🏎️', '🚙', '🚐', '🛻', '🏍️', '🚲'];
                                            $currentVehicleIcon = mb_substr($data['categories']['vehicle'], 0, 1, 'UTF-8');
                                            foreach ($vehicleIcons as $icon): ?>
                                                <option value="<?php echo htmlspecialchars($icon, ENT_QUOTES, 'UTF-8'); ?>" <?php echo ($icon === $currentVehicleIcon) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($icon, ENT_QUOTES, 'UTF-8'); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <input type="text" class="form-control" id="category_vehicle" name="category_vehicle"
                                               value="<?php echo htmlspecialchars(trim(mb_substr($data['categories']['vehicle'], 1, null, 'UTF-8'))); ?>"
                                               placeholder="Vehicle">
                                    </div>
                                    <div class="form-text">Select icon and enter category name</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_atmosphere" class="form-label">Atmosphere Category</label>
                                    <div class="input-group">
                                        <select class="form-select" style="max-width: 80px;" id="atmosphere_icon" name="atmosphere_icon">
                                            <?php
                                            $atmosphereIcons = ['🌟', '📸', '🎪', '🌅', '🌆', '🏞️', '🎯', '⭐'];
                                            $currentAtmosphereIcon = mb_substr($data['categories']['atmosphere'], 0, 1, 'UTF-8');
                                            foreach ($atmosphereIcons as $icon): ?>
                                                <option value="<?php echo htmlspecialchars($icon, ENT_QUOTES, 'UTF-8'); ?>" <?php echo ($icon === $currentAtmosphereIcon) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($icon, ENT_QUOTES, 'UTF-8'); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <input type="text" class="form-control" id="category_atmosphere" name="category_atmosphere"
                                               value="<?php echo htmlspecialchars(trim(mb_substr($data['categories']['atmosphere'], 1, null, 'UTF-8'))); ?>"
                                               placeholder="Atmosphere">
                                    </div>
                                    <div class="form-text">Select icon and enter category name</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_awards" class="form-label">Awards Category</label>
                                    <div class="input-group">
                                        <select class="form-select" style="max-width: 80px;" id="awards_icon" name="awards_icon">
                                            <?php
                                            $awardsIcons = ['🏆', '🥇', '🥈', '🥉', '🏅', '👑', '⭐', '🎖️'];
                                            $currentAwardsIcon = mb_substr($data['categories']['awards'], 0, 1, 'UTF-8');
                                            foreach ($awardsIcons as $icon): ?>
                                                <option value="<?php echo htmlspecialchars($icon, ENT_QUOTES, 'UTF-8'); ?>" <?php echo ($icon === $currentAwardsIcon) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($icon, ENT_QUOTES, 'UTF-8'); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <input type="text" class="form-control" id="category_awards" name="category_awards"
                                               value="<?php echo htmlspecialchars(trim(mb_substr($data['categories']['awards'], 1, null, 'UTF-8'))); ?>"
                                               placeholder="Awards">
                                    </div>
                                    <div class="form-text">Select icon and enter category name</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_vendors" class="form-label">Vendors Category</label>
                                    <div class="input-group">
                                        <select class="form-select" style="max-width: 80px;" id="vendors_icon" name="vendors_icon">
                                            <?php
                                            $vendorsIcons = ['🍔', '🌭', '🍕', '🥤', '🛒', '🏪', '🛍️', '☕'];
                                            $currentVendorsIcon = mb_substr($data['categories']['vendors'], 0, 1, 'UTF-8');
                                            foreach ($vendorsIcons as $icon): ?>
                                                <option value="<?php echo htmlspecialchars($icon, ENT_QUOTES, 'UTF-8'); ?>" <?php echo ($icon === $currentVendorsIcon) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($icon, ENT_QUOTES, 'UTF-8'); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <input type="text" class="form-control" id="category_vendors" name="category_vendors"
                                               value="<?php echo htmlspecialchars(trim(mb_substr($data['categories']['vendors'], 1, null, 'UTF-8'))); ?>"
                                               placeholder="Vendors">
                                    </div>
                                    <div class="form-text">Select icon and enter category name</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_people" class="form-label">People Category</label>
                                    <div class="input-group">
                                        <select class="form-select" style="max-width: 80px;" id="people_icon" name="people_icon">
                                            <?php
                                            $peopleIcons = ['👥', '👫', '👪', '🙋', '🤝', '👋', '😊', '📷'];
                                            $currentPeopleIcon = mb_substr($data['categories']['people'], 0, 1, 'UTF-8');
                                            foreach ($peopleIcons as $icon): ?>
                                                <option value="<?php echo htmlspecialchars($icon, ENT_QUOTES, 'UTF-8'); ?>" <?php echo ($icon === $currentPeopleIcon) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($icon, ENT_QUOTES, 'UTF-8'); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <input type="text" class="form-control" id="category_people" name="category_people"
                                               value="<?php echo htmlspecialchars(trim(mb_substr($data['categories']['people'], 1, null, 'UTF-8'))); ?>"
                                               placeholder="People">
                                    </div>
                                    <div class="form-text">Select icon and enter category name</div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Tips:</strong>
                            <ul class="mb-0 mt-2">
                                <li>Select an emoji icon and enter descriptive text</li>
                                <li>Keep labels short and clear</li>
                                <li>These labels appear as badges on uploaded photos</li>
                                <li>Changes take effect immediately for new photo uploads</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?php echo BASE_URL; ?>/admin/settings_event_photos" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Category Labels
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
