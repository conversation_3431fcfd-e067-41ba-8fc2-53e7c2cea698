<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Essential Open Graph Meta Tags for Facebook -->
    <meta property="fb:app_id" content="<?php echo defined('FB_APP_ID') ? FB_APP_ID : ''; ?>">
    <meta property="og:site_name" content="<?php echo APP_NAME; ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars($data['og_data']['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($data['og_data']['description']); ?>">
    <meta property="og:image" content="<?php echo $data['og_data']['image']; ?>">
    <meta property="og:url" content="<?php echo $data['og_data']['url']; ?>">
    <meta property="og:type" content="<?php echo $data['og_data']['type']; ?>">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:type" content="image/jpeg">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($data['og_data']['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($data['og_data']['description']); ?>">
    <meta name="twitter:image" content="<?php echo $data['og_data']['image']; ?>">
    
    <title><?php echo htmlspecialchars($data['og_data']['title']); ?></title>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .photo-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .photo-image {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .photo-caption {
            font-size: 18px;
            margin-bottom: 15px;
            white-space: pre-line;
            text-align: left;
        }
        .photo-details {
            color: #666;
            font-size: 14px;
            text-align: left;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .back-link:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="photo-container">
        <!-- Photo Image -->
        <img src="<?php echo $data['photo']['full_url']; ?>" 
             alt="<?php echo htmlspecialchars($data['photo']['caption'] ?: $data['photo']['category_label']); ?>"
             class="photo-image">
        
        <!-- Caption -->
        <?php if ($data['photo']['caption']): ?>
            <div class="photo-caption">
                <?php echo htmlspecialchars($data['photo']['caption']); ?>
            </div>
        <?php endif; ?>
        
        <!-- Event Details -->
        <div class="photo-details">
            <?php if ($data['photo']['event_name'] && $data['photo']['event_name'] !== 'Event Photo'): ?>
                <strong>📅 Event:</strong> <?php echo htmlspecialchars($data['photo']['event_name']); ?><br>
                
                <?php if ($data['photo']['event_date']): ?>
                    <strong>📅 Date:</strong> <?php echo date('F j, Y', strtotime($data['photo']['event_date'])); ?><br>
                <?php endif; ?>
                
                <?php if ($data['photo']['event_location']): ?>
                    <strong>📍 Location:</strong> <?php echo htmlspecialchars($data['photo']['event_location']); ?><br>
                <?php endif; ?>
            <?php endif; ?>
            
            <?php if ($data['photo']['photographer_name'] && $data['photo']['photographer_name'] !== 'Anonymous'): ?>
                <strong>📷 Photo by:</strong> <?php echo htmlspecialchars($data['photo']['photographer_name']); ?><br>
            <?php endif; ?>
        </div>
        
        <!-- Back to Event/Show Link -->
        <?php
        // Generate event/show URL for the button
        $eventUrl = BASE_URL;
        if ($data['photo']['event_type'] && $data['photo']['event_id']) {
            if ($data['photo']['event_type'] === 'event') {
                $eventUrl = BASE_URL . '/calendar/view/' . $data['photo']['event_id'];
            } elseif ($data['photo']['event_type'] === 'show') {
                $eventUrl = BASE_URL . '/show/view/' . $data['photo']['event_id'];
            }
        }
        ?>

        <?php if ($eventUrl !== BASE_URL && $data['photo']['event_name'] !== 'Event Photo'): ?>
            <a href="<?php echo $eventUrl; ?>" class="back-link">
                View <?php echo htmlspecialchars($data['photo']['event_name']); ?> Details
            </a>
        <?php else: ?>
            <a href="<?php echo BASE_URL; ?>" class="back-link">
                View More Photos at <?php echo APP_NAME; ?>
            </a>
        <?php endif; ?>
    </div>
</body>
</html>
